
        /* Variables de color mejoradas para mejor contraste y armonía */
        :root {
            /* Colores principales - Paleta cohesiva */
            --primary-blue: #2699FB;
            --primary-dark: #1e3a8a;
            --primary-medium: #3b82f6;
            --primary-light: #60a5fa;

            /* Header con gradiente profesional */
            --header-gradient: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #2563eb 100%);
            --header-overlay: rgba(30, 58, 138, 0.95);

            /* Colores de estado */
            --success-color: #10b981;
            --success-light: #34d399;
            --info-color: #06b6d4;
            --info-light: #22d3ee;
            --warning-color: #f59e0b;
            --warning-light: #fbbf24;
            --secondary-color: #6b7280;
            --secondary-light: #9ca3af;

            /* Colores neutros */
            --light-color: #f8fafc;
            --light-gray: #f1f5f9;
            --medium-gray: #e2e8f0;
            --dark-color: #1f2937;
            --dark-medium: #374151;
            --body-bg: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);

            /* Efectos y sombras */
            --border-radius: 12px;
            --border-radius-sm: 8px;
            --box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --box-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-fast: all 0.15s ease-out;
        }
        
        /* Estilos base del body */
        body {
            background: var(--body-bg);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            color: var(--dark-color);
            line-height: 1.6;
        }

        /* Header profesional con gradiente mejorado - Override existing styles */
        .simple-header, .inteletgroup-header, .inteletgroup-header-override, .site-header {
            background: var(--header-gradient) !important;
            color: white !important;
            box-shadow: var(--box-shadow-lg) !important;
            margin-bottom: 2rem !important;
            position: sticky !important;
            top: 0 !important;
            z-index: 1000 !important;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
            padding: 0 !important;
            backdrop-filter: blur(10px) !important;
            height: auto !important;
        }

        /* Overlay para mejorar contraste */
        .simple-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--header-overlay);
            z-index: -1;
        }

        /* Contenedor principal del header - Override existing styles */
        .header-container {
            display: flex !important;
            align-items: center !important;
            justify-content: space-between !important;
            padding: 1rem 0 !important;
            position: relative !important;
            z-index: 1 !important;
            max-width: none !important;
            margin: 0 auto !important;
            height: auto !important;
        }

        /* Logo y nombre de la empresa */
        .brand-section {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        /* Logo wrapper mejorado */
        .logo-wrapper {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 48px;
            height: 48px;
            background: rgba(255, 255, 255, 0.15);
            border-radius: 50%;
            border: 2px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            transition: var(--transition);
        }

        .logo-wrapper:hover {
            background: rgba(255, 255, 255, 0.25);
            transform: scale(1.05);
        }

        .logo-wrapper img {
            height: 28px;
            width: 28px;
            object-fit: contain;
            border-radius: 50%;
        }

        /* Estilos para la información del sitio */
        .site-info {
            display: flex;
            flex-direction: column;
        }

        .site-title {
            font-size: 1.5rem !important;
            font-weight: 700 !important;
            margin: 0 !important;
            padding: 0 !important;
            color: white !important;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
            letter-spacing: -0.025em !important;
        }

        .site-subtitle {
            font-size: 0.875rem !important;
            opacity: 0.9 !important;
            margin: 0 !important;
            padding: 0 !important;
            color: rgba(255, 255, 255, 0.9) !important;
            font-weight: 500 !important;
        }

        /* Usuario y acciones */
        .user-section {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        /* Estilos para el nombre de usuario */
        .user-info-container {
            text-align: right;
            line-height: 1.3;
            padding: 0.5rem 1rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: var(--border-radius-sm);
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
        }

        .user-name {
            font-size: 1rem;
            font-weight: 600;
            color: white;
            margin: 0;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .user-role {
            font-size: 0.8rem;
            opacity: 0.9;
            color: rgba(255, 255, 255, 0.8);
            margin: 0;
            font-weight: 500;
        }

        /* Botones de navegación mejorados */
        .nav-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 0.6rem 1rem;
            font-size: 0.9rem;
            border-radius: 25px;
            transition: var(--transition);
            display: flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            backdrop-filter: blur(10px);
            gap: 0.5rem;
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.5);
            color: white;
            transform: scale(1.05);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .nav-btn:active {
            transform: scale(0.95);
        }
        
        /* Cards modernos */
        .modern-card {
            background: rgba(255, 255, 255, 0.95);
            border: none;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            backdrop-filter: blur(10px);
            transition: var(--transition);
        }

        .modern-card:hover {
            box-shadow: var(--box-shadow-lg);
            transform: translateY(-2px);
        }

        .search-card {
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.05), rgba(52, 211, 153, 0.05));
            border-left: 4px solid var(--success-color);
        }
        
        .info-card {
            background: linear-gradient(135deg, rgba(6, 182, 212, 0.05), rgba(34, 211, 238, 0.05));
            border-left: 4px solid var(--info-color);
        }
        
        /* Tabla de prospectos moderna */
        .prospects-table {
            background: white;
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--box-shadow);
        }

        .prospects-table .table {
            margin-bottom: 0;
        }

        .prospects-table th {
            background: var(--header-gradient);
            color: white;
            border: none;
            font-weight: 600;
            padding: 1rem 0.75rem;
        }

        .prospects-table td {
            padding: 0.75rem;
            border-color: var(--medium-gray);
            vertical-align: middle;
        }

        .prospects-table tbody tr:hover {
            background-color: rgba(59, 130, 246, 0.05);
        }

        .badge-documents {
            background: var(--info-color);
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 15px;
            font-size: 0.75rem;
        }

        .document-item {
            background: white;
            border: 1px solid var(--medium-gray);
            border-radius: var(--border-radius-sm);
            padding: 1rem;
            margin-bottom: 1rem;
            transition: var(--transition);
        }
        
        .document-item:hover {
            box-shadow: var(--box-shadow);
            transform: translateY(-2px);
        }
        
        .file-icon {
            font-size: 2rem;
            margin-right: 1rem;
        }
        
        .upload-area {
            border: 2px dashed var(--primary-medium);
            border-radius: var(--border-radius-sm);
            padding: 2rem;
            text-align: center;
            background: rgba(59, 130, 246, 0.05);
            transition: var(--transition);
        }

        .upload-area:hover {
            border-color: var(--primary-dark);
            background: rgba(59, 130, 246, 0.1);
        }

        /* Estilos para la tabla de documentos */
        .document-table-row:hover {
            background-color: rgba(59, 130, 246, 0.05) !important;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .file-icon-large {
            font-size: 1.5rem;
            color: var(--primary-medium);
        }

        .btn-group .btn {
            border-radius: 0;
        }

        .btn-group .btn:first-child {
            border-top-left-radius: 0.375rem;
            border-bottom-left-radius: 0.375rem;
        }

        .btn-group .btn:last-child {
            border-top-right-radius: 0.375rem;
            border-bottom-right-radius: 0.375rem;
        }

        .document-name {
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .client-info {
            max-width: 180px;
        }

        /* Badges mejorados */
        .file-extension-badge {
            background: linear-gradient(135deg, var(--secondary-color), var(--secondary-light));
            color: white;
            font-weight: 600;
            font-size: 0.7rem;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .header-container {
                padding: 0.75rem 0;
            }

            .brand-section {
                gap: 0.75rem;
            }

            .site-title {
                font-size: 1.25rem;
            }

            .site-subtitle {
                font-size: 0.8rem;
            }

            .user-info-container {
                padding: 0.5rem 0.75rem;
            }

            .user-name {
                font-size: 0.9rem;
            }

            .user-role {
                font-size: 0.75rem;
            }
        }

        @media (max-width: 576px) {
            .user-info-container {
                display: none;
            }

            .site-title {
                font-size: 1.1rem;
            }

            .site-subtitle {
                font-size: 0.75rem;
            }
        }
