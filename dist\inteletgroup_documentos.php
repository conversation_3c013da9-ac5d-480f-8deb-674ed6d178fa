<?php
// Redirigir a la versión enhanced ya que las tablas sí existen
header('Location: inteletgroup_documentos_enhanced.php');
exit;

// El código original se mantiene abajo por si se necesita volver a la versión anterior
/*
// Configuración de errores para desarrollo
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once 'cache_utils.php';
session_start();

// Aplicar headers anti-caché
no_cache_headers();

// Verificar autenticación
if (!isset($_SESSION['usuario_id']) || !isset($_SESSION['proyecto']) || $_SESSION['proyecto'] !== 'inteletGroup') {
    $errorMsg = urlencode("Usuario no autenticado o sin permisos para InteletGroup.");
    header('Location: ' . version_url('login.php?error=' . $errorMsg));
    exit;
}

// Incluir conexión a la base de datos
error_log("INTELETGROUP_DOCUMENTOS: Incluyendo con_db.php");
require_once 'con_db.php';
error_log("INTELETGROUP_DOCUMENTOS: con_db.php incluido exitosamente");

// Obtener información del usuario logueado
$usuario_id = $_SESSION['usuario_id'];
$nombre_usuario = $_SESSION['nombre_usuario'] ?? 'Usuario';
$proyecto = $_SESSION['proyecto'] ?? 'inteletGroup';

error_log("INTELETGROUP_DOCUMENTOS: Usuario ID: $usuario_id, Nombre: $nombre_usuario");

// Verificar si la variable mysqli está definida
error_log("INTELETGROUP_DOCUMENTOS: Verificando variable mysqli - isset: " . (isset($mysqli) ? 'true' : 'false'));

// Usar la conexión mysqli del archivo con_db.php
if (!isset($mysqli)) {
    error_log("Error crítico: Variable mysqli no definida en con_db.php");
    die("Error de conexión a la base de datos. Variable mysqli no definida. Por favor, contacte al administrador.");
}

error_log("INTELETGROUP_DOCUMENTOS: mysqli definida, verificando connect_error");

if ($mysqli->connect_error) {
    error_log("Error crítico: Error de conexión MySQL: " . $mysqli->connect_error);
    die("Error de conexión a la base de datos. Error de conexión: " . $mysqli->connect_error);
}

$conexion = $mysqli;
error_log("INTELETGROUP_DOCUMENTOS: Conexión establecida exitosamente");

// Obtener todos los prospectos del usuario
$prospectos_usuario = [];
try {
    // Verificar que la tabla existe
    $check_table = $conexion->query("SHOW TABLES LIKE 'tb_inteletgroup_prospectos'");
    if ($check_table->num_rows == 0) {
        error_log("Tabla tb_inteletgroup_prospectos no existe");
        $prospectos_usuario = [];
    } else {
        $stmt_prospectos = $conexion->prepare("
            SELECT p.id, p.rut_cliente, p.razon_social, p.rubro, p.email, 
                   p.telefono_celular, p.nombre_ejecutivo, p.fecha_registro,
                   COALESCE((SELECT COUNT(*) FROM tb_inteletgroup_documentos d
                            WHERE d.prospecto_id = p.id AND d.estado = 'Activo'), 0) as total_documentos
            FROM tb_inteletgroup_prospectos p
            WHERE p.usuario_id = ?
            ORDER BY p.fecha_registro DESC
        ");

        if ($stmt_prospectos) {
            $stmt_prospectos->bind_param("i", $usuario_id);
            $stmt_prospectos->execute();

            // Usar bind_result en lugar de get_result para compatibilidad
            $stmt_prospectos->bind_result($id, $rut_cliente, $razon_social, $rubro, $email, $telefono_celular, $nombre_ejecutivo, $fecha_registro, $total_documentos);

            while ($stmt_prospectos->fetch()) {
                // Debug temporal
                error_log("Debug prospecto: RUT=" . $rut_cliente . ", Razón=" . $razon_social . ", Email=" . $email);

                $prospectos_usuario[] = [
                    'id' => $id,
                    'rut_cliente' => $rut_cliente,
                    'razon_social' => $razon_social,
                    'rubro' => $rubro,
                    'email' => $email,
                    'telefono_celular' => $telefono_celular,
                    'nombre_ejecutivo' => $nombre_ejecutivo,
                    'fecha_registro' => $fecha_registro,
                    'total_documentos' => $total_documentos
                ];
            }
            $stmt_prospectos->close();
        } else {
            error_log("Error al preparar consulta de prospectos: " . $conexion->error);
            $prospectos_usuario = [];
        }
    }
} catch (Exception $e) {
    error_log("Error obteniendo prospectos: " . $e->getMessage());
    $prospectos_usuario = [];
}

// Obtener todos los documentos del usuario con información del prospecto
$todos_documentos = [];
try {
    $stmt_all_docs = $conexion->prepare("
        SELECT d.id, d.prospecto_id, d.usuario_id, d.rut_cliente, d.nombre_archivo, d.nombre_original,
               d.tipo_archivo, d.tamaño_archivo, d.ruta_archivo, d.fecha_subida, d.estado,
               p.razon_social, p.rubro, p.email, p.telefono_celular, p.nombre_ejecutivo
        FROM tb_inteletgroup_documentos d
        LEFT JOIN tb_inteletgroup_prospectos p ON d.prospecto_id = p.id
        WHERE d.usuario_id = ? AND d.estado = 'Activo'
        ORDER BY d.fecha_subida DESC
    ");

    if ($stmt_all_docs) {
        $stmt_all_docs->bind_param("i", $usuario_id);
        $stmt_all_docs->execute();

        // Usar bind_result en lugar de get_result para compatibilidad
        // Orden correcto según la estructura de la tabla: id, prospecto_id, usuario_id, rut_cliente, nombre_archivo, nombre_original, tipo_archivo, tamaño_archivo, ruta_archivo, fecha_subida, estado
        // Luego los campos del JOIN: razon_social, rubro, email, telefono_celular, nombre_ejecutivo
        // ORDEN CORRECTO según la consulta SQL: id, prospecto_id, usuario_id, rut_cliente, nombre_archivo, nombre_original, tipo_archivo, tamaño_archivo, ruta_archivo, fecha_subida, estado, razon_social, rubro, email, telefono_celular, nombre_ejecutivo
        $stmt_all_docs->bind_result($doc_id, $doc_prospecto_id, $doc_usuario_id, $doc_rut_cliente, $doc_nombre_archivo, $doc_nombre_original, $doc_tipo_archivo, $doc_tamaño_archivo, $doc_ruta_archivo, $doc_fecha_subida, $doc_estado, $razon_social, $rubro, $email, $telefono_celular, $nombre_ejecutivo);

        while ($stmt_all_docs->fetch()) {
            $todos_documentos[] = [
                'id' => $doc_id,
                'prospecto_id' => $doc_prospecto_id,
                'usuario_id' => $doc_usuario_id,
                'rut_cliente' => $doc_rut_cliente,
                'nombre_original' => $doc_nombre_original, // CORRECTO: nombre_original es el nombre que subió el usuario
                'nombre_archivo' => $doc_nombre_archivo, // CORRECTO: nombre_archivo es el nombre en el servidor
                'ruta_archivo' => $doc_ruta_archivo, // CORRECTO: ruta_archivo es la ruta del archivo
                'tamaño_archivo' => $doc_tamaño_archivo,
                'tipo_archivo' => $doc_tipo_archivo, // CORRECTO: tipo_archivo es el tipo MIME
                'fecha_subida' => $doc_fecha_subida,
                'estado' => $doc_estado,
                'razon_social' => $razon_social,
                'rubro' => $rubro,
                'email' => $email,
                'telefono_celular' => $telefono_celular,
                'nombre_ejecutivo' => $nombre_ejecutivo
            ];
        }
        $stmt_all_docs->close();
    }
} catch (Exception $e) {
    error_log("Error obteniendo todos los documentos: " . $e->getMessage());
    $todos_documentos = [];
}

// Procesar búsqueda por RUT
$rut_busqueda = '';
$prospecto_info = null;
$documentos = [];
$mensaje = '';

if (isset($_POST['buscar_rut']) && !empty($_POST['rut_cliente'])) {
    $rut_busqueda = trim($_POST['rut_cliente']);
    
    // Buscar prospecto por RUT
    $stmt = $conexion->prepare("
        SELECT p.id, p.usuario_id, p.nombre_ejecutivo, p.rut_cliente, p.razon_social, p.rubro,
               p.direccion_comercial, p.telefono_celular, p.email, p.numero_pos, p.tipo_cuenta,
               p.numero_cuenta_bancaria, p.dias_atencion, p.horario_atencion, p.contrata_boleta,
               p.competencia_actual, p.fecha_registro, u.nombre_usuario as ejecutivo_nombre
        FROM tb_inteletgroup_prospectos p
        LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
        WHERE p.rut_cliente = ?
    ");
    $stmt->bind_param("s", $rut_busqueda);
    $stmt->execute();

    // Usar bind_result para compatibilidad
    $p_id = $p_usuario_id = $p_nombre_ejecutivo = $p_rut_cliente = $p_razon_social = null;
    $p_rubro = $p_direccion_comercial = $p_telefono_celular = $p_email = $p_numero_pos = null;
    $p_tipo_cuenta = $p_numero_cuenta_bancaria = $p_dias_atencion = $p_horario_atencion = null;
    $p_contrata_boleta = $p_competencia_actual = $p_fecha_registro = null;
    $u_nombre_usuario = null;

    $stmt->bind_result(
        $p_id, $p_usuario_id, $p_nombre_ejecutivo, $p_rut_cliente, $p_razon_social, $p_rubro,
        $p_direccion_comercial, $p_telefono_celular, $p_email, $p_numero_pos, $p_tipo_cuenta,
        $p_numero_cuenta_bancaria, $p_dias_atencion, $p_horario_atencion, $p_contrata_boleta,
        $p_competencia_actual, $p_fecha_registro, $u_nombre_usuario
    );

    if ($stmt->fetch()) {
        $prospecto_info = [
            'id' => $p_id,
            'usuario_id' => $p_usuario_id,
            'nombre_ejecutivo' => $p_nombre_ejecutivo,
            'rut_cliente' => $p_rut_cliente,
            'razon_social' => $p_razon_social,
            'rubro' => $p_rubro,
            'direccion_comercial' => $p_direccion_comercial,
            'telefono_celular' => $p_telefono_celular,
            'email' => $p_email,
            'numero_pos' => $p_numero_pos,
            'tipo_cuenta' => $p_tipo_cuenta,
            'numero_cuenta_bancaria' => $p_numero_cuenta_bancaria,
            'dias_atencion' => $p_dias_atencion,
            'horario_atencion' => $p_horario_atencion,
            'contrata_boleta' => $p_contrata_boleta,
            'competencia_actual' => $p_competencia_actual,
            'fecha_registro' => $p_fecha_registro,
            'nombre_usuario' => $u_nombre_usuario
        ];
        $stmt->close();

        // Obtener documentos del prospecto
        $stmt_docs = $conexion->prepare("
            SELECT id, prospecto_id, rut_cliente, nombre_archivo, ruta_archivo,
                   tipo_archivo, tamaño_archivo, estado, fecha_subida
            FROM tb_inteletgroup_documentos
            WHERE rut_cliente = ? AND estado = 'Activo'
            ORDER BY fecha_subida DESC
        ");
        $stmt_docs->bind_param("s", $rut_busqueda);
        $stmt_docs->execute();

        // Usar bind_result para documentos
        $d_id = $d_prospecto_id = $d_rut_cliente = $d_nombre_archivo = $d_ruta_archivo = null;
        $d_tipo_archivo = $d_tamaño_archivo = $d_estado = $d_fecha_subida = null;

        $stmt_docs->bind_result(
            $d_id, $d_prospecto_id, $d_rut_cliente, $d_nombre_archivo, $d_ruta_archivo,
            $d_tipo_archivo, $d_tamaño_archivo, $d_estado, $d_fecha_subida
        );

        while ($stmt_docs->fetch()) {
            $documentos[] = [
                'id' => $d_id,
                'prospecto_id' => $d_prospecto_id,
                'rut_cliente' => $d_rut_cliente,
                'nombre_archivo' => $d_nombre_archivo,
                'ruta_archivo' => $d_ruta_archivo,
                'tipo_archivo' => $d_tipo_archivo,
                'tamaño_archivo' => $d_tamaño_archivo,
                'estado' => $d_estado,
                'fecha_subida' => $d_fecha_subida
            ];
        }
        $stmt_docs->close();
    } else {
        $mensaje = "No se encontró ningún prospecto con el RUT: $rut_busqueda";
    }
}

// Procesar subida de nuevos documentos
if (isset($_POST['subir_documento']) && !empty($_POST['rut_cliente_doc'])) {
    $rut_cliente = trim($_POST['rut_cliente_doc']);
    
    // Verificar que el prospecto existe
    $stmt = $conexion->prepare("SELECT id FROM tb_inteletgroup_prospectos WHERE rut_cliente = ?");
    $stmt->bind_param("s", $rut_cliente);
    $stmt->execute();

    // Usar bind_result para compatibilidad
    $prospecto_id = null;
    $stmt->bind_result($prospecto_id);

    if ($stmt->fetch()) {
        $stmt->close();
        
        if (isset($_FILES['nuevo_documento']) && $_FILES['nuevo_documento']['error'] === UPLOAD_ERR_OK) {
            $upload_dir = 'uploads/inteletgroup_prospectos/';
            
            // Crear directorio si no existe
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }
            
            $file_name = $_FILES['nuevo_documento']['name'];
            $file_type = $_FILES['nuevo_documento']['type'];
            $file_size = $_FILES['nuevo_documento']['size'];
            $file_tmp = $_FILES['nuevo_documento']['tmp_name'];
            
            $allowed_types = [
                'application/pdf',
                'application/msword',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'image/jpeg',
                'image/jpg', 
                'image/png',
                'application/vnd.ms-excel',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            ];
            
            $max_size = 5 * 1024 * 1024; // 5MB
            
            if (in_array($file_type, $allowed_types) && $file_size <= $max_size) {
                $extension = pathinfo($file_name, PATHINFO_EXTENSION);
                $unique_name = $rut_cliente . '_' . time() . '.' . $extension;
                $file_path = $upload_dir . $unique_name;
                
                if (move_uploaded_file($file_tmp, $file_path)) {
                    // Insertar documento en la base de datos
                    $stmt_insert = $conexion->prepare("
                        INSERT INTO tb_inteletgroup_documentos (
                            prospecto_id, usuario_id, rut_cliente, nombre_archivo, 
                            nombre_original, tipo_archivo, tamaño_archivo, ruta_archivo
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    ");
                    $stmt_insert->bind_param("iissssis", 
                        $prospecto_id, $usuario_id, $rut_cliente, $unique_name,
                        $file_name, $file_type, $file_size, $file_path
                    );
                    
                    if ($stmt_insert->execute()) {
                        // Registrar en bitácora
                        $stmt_bitacora = $conexion->prepare("
                            INSERT INTO tb_inteletgroup_prospecto_bitacora (
                                prospecto_id, usuario_id, accion, descripcion, ip_address, user_agent
                            ) VALUES (?, ?, 'Subir Documento', ?, ?, ?)
                        ");
                        $descripcion = "Documento subido: $file_name";
                        $ip_address = $_SERVER['REMOTE_ADDR'] ?? '';
                        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
                        
                        $stmt_bitacora->bind_param("iisss", $prospecto_id, $usuario_id, $descripcion, $ip_address, $user_agent);
                        $stmt_bitacora->execute();
                        
                        $mensaje = "Documento subido exitosamente";
                        
                        // Recargar documentos
                        $rut_busqueda = $rut_cliente;
                        $_POST['rut_cliente'] = $rut_cliente;
                        $_POST['buscar_rut'] = true;
                        
                        // Re-ejecutar búsqueda para actualizar la vista
                        $stmt = $conexion->prepare("
                            SELECT p.id, p.usuario_id, p.nombre_ejecutivo, p.rut_cliente, p.razon_social, p.rubro,
                                   p.direccion_comercial, p.telefono_celular, p.email, p.numero_pos, p.tipo_cuenta,
                                   p.numero_cuenta_bancaria, p.dias_atencion, p.horario_atencion, p.contrata_boleta,
                                   p.competencia_actual, p.fecha_registro, u.nombre_usuario as ejecutivo_nombre
                            FROM tb_inteletgroup_prospectos p
                            LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                            WHERE p.rut_cliente = ?
                        ");
                        $stmt->bind_param("s", $rut_busqueda);
                        $stmt->execute();

                        // Usar bind_result para compatibilidad
                        $p_id = $p_usuario_id = $p_nombre_ejecutivo = $p_rut_cliente = $p_razon_social = null;
                        $p_rubro = $p_direccion_comercial = $p_telefono_celular = $p_email = $p_numero_pos = null;
                        $p_tipo_cuenta = $p_numero_cuenta_bancaria = $p_dias_atencion = $p_horario_atencion = null;
                        $p_contrata_boleta = $p_competencia_actual = $p_fecha_registro = null;
                        $u_nombre_usuario = null;

                        $stmt->bind_result(
                            $p_id, $p_usuario_id, $p_nombre_ejecutivo, $p_rut_cliente, $p_razon_social, $p_rubro,
                            $p_direccion_comercial, $p_telefono_celular, $p_email, $p_numero_pos, $p_tipo_cuenta,
                            $p_numero_cuenta_bancaria, $p_dias_atencion, $p_horario_atencion, $p_contrata_boleta,
                            $p_competencia_actual, $p_fecha_registro, $u_nombre_usuario
                        );

                        if ($stmt->fetch()) {
                            $prospecto_info = [
                                'id' => $p_id,
                                'usuario_id' => $p_usuario_id,
                                'nombre_ejecutivo' => $p_nombre_ejecutivo,
                                'rut_cliente' => $p_rut_cliente,
                                'razon_social' => $p_razon_social,
                                'rubro' => $p_rubro,
                                'direccion_comercial' => $p_direccion_comercial,
                                'telefono_celular' => $p_telefono_celular,
                                'email' => $p_email,
                                'numero_pos' => $p_numero_pos,
                                'tipo_cuenta' => $p_tipo_cuenta,
                                'numero_cuenta_bancaria' => $p_numero_cuenta_bancaria,
                                'dias_atencion' => $p_dias_atencion,
                                'horario_atencion' => $p_horario_atencion,
                                'contrata_boleta' => $p_contrata_boleta,
                                'competencia_actual' => $p_competencia_actual,
                                'fecha_registro' => $p_fecha_registro,
                                'nombre_usuario' => $u_nombre_usuario
                            ];
                        }
                        $stmt->close();

                        $documentos = [];
                        $stmt_docs = $conexion->prepare("
                            SELECT id, prospecto_id, rut_cliente, nombre_archivo, ruta_archivo,
                                   tipo_archivo, tamaño_archivo, estado, fecha_subida
                            FROM tb_inteletgroup_documentos
                            WHERE rut_cliente = ? AND estado = 'Activo'
                            ORDER BY fecha_subida DESC
                        ");
                        $stmt_docs->bind_param("s", $rut_busqueda);
                        $stmt_docs->execute();

                        // Usar bind_result para documentos
                        $d_id = $d_prospecto_id = $d_rut_cliente = $d_nombre_archivo = $d_ruta_archivo = null;
                        $d_tipo_archivo = $d_tamaño_archivo = $d_estado = $d_fecha_subida = null;

                        $stmt_docs->bind_result(
                            $d_id, $d_prospecto_id, $d_rut_cliente, $d_nombre_archivo, $d_ruta_archivo,
                            $d_tipo_archivo, $d_tamaño_archivo, $d_estado, $d_fecha_subida
                        );

                        while ($stmt_docs->fetch()) {
                            $documentos[] = [
                                'id' => $d_id,
                                'prospecto_id' => $d_prospecto_id,
                                'rut_cliente' => $d_rut_cliente,
                                'nombre_archivo' => $d_nombre_archivo,
                                'ruta_archivo' => $d_ruta_archivo,
                                'tipo_archivo' => $d_tipo_archivo,
                                'tamaño_archivo' => $d_tamaño_archivo,
                                'estado' => $d_estado,
                                'fecha_subida' => $d_fecha_subida
                            ];
                        }
                        $stmt_docs->close();
                    } else {
                        $mensaje = "Error al guardar el documento en la base de datos";
                    }
                } else {
                    $mensaje = "Error al subir el archivo";
                }
            } else {
                $mensaje = "Tipo de archivo no permitido o archivo muy grande";
            }
        } else {
            $mensaje = "No se seleccionó ningún archivo";
        }
    } else {
        $mensaje = "No se encontró el prospecto con el RUT especificado";
    }
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>InteletGroup - Gestión de Documentos</title>
    <?php echo no_cache_meta(); ?>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
    
    <link rel="stylesheet" href="css/inteletgroup_documentos.css">
</head>
<body>
    <!-- Header profesional inspirado en form_inteletgroup.php -->
    <header class="simple-header inteletgroup-header-override">
        <div class="container">
            <div class="header-container">
                <!-- Logo y nombre del sitio -->
                <div class="brand-section">
                    <div class="logo-wrapper">
                        <img src="img/icons/logo_intelet.jpg" alt="Logo InteletGroup"
                             onerror="this.innerHTML='<i class=\'bi bi-building\' style=\'font-size: 1.5rem; color: white;\'></i>'">
                    </div>
                    <div class="site-info">
                        <h1 class="site-title">InteletGroup</h1>
                        <span class="site-subtitle">Gestión de Documentos</span>
                    </div>
                </div>
                
                <!-- Usuario y acciones -->
                <div class="user-section">
                    <div class="user-info-container">
                        <div class="user-name"><?php echo htmlspecialchars($nombre_usuario); ?></div>
                        <div class="user-role"><?php echo htmlspecialchars($proyecto); ?></div>
                    </div>
                    <a href="form_inteletgroup.php" class="nav-btn" title="Volver al Panel">
                        <i class="bi bi-arrow-left"></i> Volver
                    </a>
                    <a href="logout.php" class="nav-btn" title="Cerrar sesión">
                        <i class="bi bi-box-arrow-right"></i> Salir
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div class="container mt-4">
        <!-- Mensajes -->
        <?php if (!empty($mensaje)): ?>
        <div class="alert <?php echo strpos($mensaje, 'Error') !== false || strpos($mensaje, 'No se encontró') !== false ? 'alert-danger' : 'alert-success'; ?> alert-dismissible fade show" role="alert">
            <i class="bi bi-<?php echo strpos($mensaje, 'Error') !== false || strpos($mensaje, 'No se encontró') !== false ? 'exclamation-triangle' : 'check-circle'; ?>-fill me-2"></i>
            <?php echo htmlspecialchars($mensaje); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- DEBUG: Inicio de tabla de documentos -->

        <!-- Tabla Principal de Documentos -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="prospects-table">
                    <div class="p-3">
                        <h5 class="mb-3">
                            <i class="bi bi-files me-2"></i>
                            Todos mis Documentos (<?php echo count($todos_documentos); ?>)
                        </h5>
                        <p class="text-muted mb-0">
                            <i class="bi bi-info-circle me-1"></i>
                            Vista completa de todos los documentos subidos por prospecto
                        </p>
                    </div>

                    <?php if (empty($todos_documentos)): ?>
                    <div class="text-center py-5">
                        <i class="bi bi-file-earmark-plus text-muted" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-3">No tienes documentos subidos aún</p>
                        <p class="text-muted">Usuario ID: <?php echo $usuario_id; ?></p>
                        <a href="form_inteletgroup.php" class="btn btn-primary">
                            <i class="bi bi-plus-circle me-1"></i>
                            Registrar Prospecto y Subir Documentos
                        </a>
                    </div>
                    <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>RUT Cliente</th>
                                    <th>Razón Social</th>
                                    <th>Documento</th>
                                    <th>Tipo</th>
                                    <th>Tamaño</th>
                                    <th>Fecha Subida</th>
                                    <th>Acciones</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($todos_documentos as $doc): ?>
                                <tr class="document-table-row">
                                    <td>
                                        <strong class="text-primary"><?php echo htmlspecialchars($doc['rut_cliente']); ?></strong>
                                    </td>
                                    <td>
                                        <div class="client-info">
                                            <strong><?php echo htmlspecialchars($doc['razon_social'] ?? 'N/A'); ?></strong>
                                            <br>
                                            <small class="text-muted"><?php echo htmlspecialchars($doc['rubro'] ?? 'N/A'); ?></small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-file-earmark-pdf file-icon-large me-2"></i>
                                            <div class="document-name">
                                                <strong title="<?php echo htmlspecialchars($doc['nombre_original']); ?>">
                                                    <?php echo htmlspecialchars($doc['nombre_original']); ?>
                                                </strong>
                                                <br>
                                                <small class="text-muted" title="<?php echo htmlspecialchars($doc['nombre_archivo']); ?>">
                                                    <?php echo htmlspecialchars($doc['nombre_archivo']); ?>
                                                </small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="file-extension-badge">
                                            <?php echo strtoupper(pathinfo($doc['nombre_original'], PATHINFO_EXTENSION)); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <small class="text-muted"><?php echo number_format($doc['tamaño_archivo'] / 1024, 2) . ' KB'; ?></small>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            <?php echo date('d/m/Y H:i', strtotime($doc['fecha_subida'])); ?>
                                        </small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="descargar_documento.php?id=<?php echo $doc['id']; ?>&action=view"
                                               class="btn btn-sm btn-outline-primary" target="_blank" title="Ver/Abrir">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                            <a href="descargar_documento.php?id=<?php echo $doc['id']; ?>&action=download"
                                               class="btn btn-sm btn-outline-success" title="Descargar">
                                                <i class="bi bi-download"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Tabla de Prospectos del Usuario (Resumen) -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="prospects-table">
                    <div class="p-3">
                        <h5 class="mb-3">
                            <i class="bi bi-people-fill me-2"></i>
                            Resumen de Prospectos (<?php echo count($prospectos_usuario); ?>)
                        </h5>
                    </div>

                    <?php if (empty($prospectos_usuario)): ?>
                    <div class="text-center py-5">
                        <i class="bi bi-person-plus text-muted" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-3">No tienes prospectos registrados aún</p>
                        <a href="form_inteletgroup.php" class="btn btn-primary">
                            <i class="bi bi-plus-circle me-1"></i>
                            Registrar Primer Prospecto
                        </a>
                    </div>
                    <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>RUT</th>
                                    <th>Razón Social</th>
                                    <th>Rubro</th>
                                    <th>Email</th>
                                    <th>Teléfono</th>
                                    <th>Documentos</th>
                                    <th>Fecha Registro</th>
                                    <th>Acciones</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($prospectos_usuario as $prospecto): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo htmlspecialchars($prospecto['rut_cliente']); ?></strong>
                                    </td>
                                    <td><?php echo htmlspecialchars($prospecto['razon_social']); ?></td>
                                    <td>
                                        <small class="text-muted"><?php echo htmlspecialchars($prospecto['rubro']); ?></small>
                                    </td>
                                    <td>
                                        <a href="mailto:<?php echo htmlspecialchars($prospecto['email']); ?>" class="text-decoration-none">
                                            <small><?php echo htmlspecialchars($prospecto['email']); ?></small>
                                        </a>
                                    </td>
                                    <td>
                                        <a href="tel:<?php echo htmlspecialchars($prospecto['telefono_celular']); ?>" class="text-decoration-none">
                                            <small><?php echo htmlspecialchars($prospecto['telefono_celular']); ?></small>
                                        </a>
                                    </td>
                                    <td>
                                        <span class="badge-documents">
                                            <?php echo $prospecto['total_documentos']; ?> docs
                                        </span>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            <?php echo date('d/m/Y', strtotime($prospecto['fecha_registro'])); ?>
                                        </small>
                                    </td>
                                    <td>
                                        <form method="POST" class="d-inline">
                                            <input type="hidden" name="rut_cliente" value="<?php echo htmlspecialchars($prospecto['rut_cliente']); ?>">
                                            <button type="submit" name="buscar_rut" class="btn btn-sm btn-outline-primary" title="Ver Documentos">
                                                <i class="bi bi-files"></i>
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Búsqueda por RUT -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card modern-card search-card">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="bi bi-search me-2"></i>
                            Buscar Prospecto por RUT
                        </h5>
                        <form method="POST" class="row g-3">
                            <div class="col-md-8">
                                <label for="rut_cliente" class="form-label">RUT del Cliente</label>
                                <input type="text" class="form-control" id="rut_cliente" name="rut_cliente" 
                                       placeholder="12345678-9" value="<?php echo htmlspecialchars($rut_busqueda); ?>" required>
                            </div>
                            <div class="col-md-4 d-flex align-items-end">
                                <button type="submit" name="buscar_rut" class="btn btn-primary w-100">
                                    <i class="bi bi-search me-1"></i>
                                    Buscar
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <?php if ($prospecto_info): ?>
        <!-- Información del Prospecto -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card modern-card info-card">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="bi bi-building me-2"></i>
                            Información del Prospecto
                        </h5>
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>RUT:</strong> <?php echo htmlspecialchars($prospecto_info['rut_cliente']); ?></p>
                                <p><strong>Razón Social:</strong> <?php echo htmlspecialchars($prospecto_info['razon_social']); ?></p>
                                <p><strong>Rubro:</strong> <?php echo htmlspecialchars($prospecto_info['rubro']); ?></p>
                                <p><strong>Email:</strong> <?php echo htmlspecialchars($prospecto_info['email']); ?></p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>Ejecutivo:</strong> <?php echo htmlspecialchars($prospecto_info['ejecutivo_nombre']); ?></p>
                                <p><strong>Teléfono:</strong> <?php echo htmlspecialchars($prospecto_info['telefono_celular']); ?></p>
                                <p><strong>Competencia:</strong> <?php echo htmlspecialchars($prospecto_info['competencia_actual']); ?></p>
                                <p><strong>Fecha Registro:</strong> <?php echo date('d/m/Y H:i', strtotime($prospecto_info['fecha_registro'])); ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Subir Nuevo Documento -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card modern-card">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="bi bi-cloud-upload me-2"></i>
                            Subir Nuevo Documento
                        </h5>
                        <form method="POST" enctype="multipart/form-data">
                            <input type="hidden" name="rut_cliente_doc" value="<?php echo htmlspecialchars($prospecto_info['rut_cliente']); ?>">
                            <div class="upload-area">
                                <i class="bi bi-cloud-upload text-primary" style="font-size: 3rem;"></i>
                                <h6 class="mt-3">Seleccionar Archivo</h6>
                                <input type="file" class="form-control mt-3" name="nuevo_documento" 
                                       accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.xlsx,.xls" required>
                                <small class="text-muted">
                                    Formatos permitidos: PDF, DOC, DOCX, JPG, JPEG, PNG, XLSX, XLS. Máximo 5MB.
                                </small>
                                <div class="mt-3">
                                    <button type="submit" name="subir_documento" class="btn btn-success">
                                        <i class="bi bi-upload me-1"></i>
                                        Subir Documento
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Lista de Documentos -->
        <div class="row">
            <div class="col-12">
                <div class="card modern-card">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="bi bi-files me-2"></i>
                            Documentos (<?php echo count($documentos); ?>)
                        </h5>
                        
                        <?php if (empty($documentos)): ?>
                        <div class="text-center py-4">
                            <i class="bi bi-file-earmark text-muted" style="font-size: 3rem;"></i>
                            <p class="text-muted mt-3">No hay documentos subidos para este prospecto</p>
                        </div>
                        <?php else: ?>
                        <?php foreach ($documentos as $doc): ?>
                        <div class="document-item">
                            <div class="row align-items-center">
                                <div class="col-md-8">
                                    <div class="d-flex align-items-center">
                                        <i class="bi bi-file-earmark-<?php echo getFileIcon($doc['tipo_archivo']); ?> file-icon text-primary"></i>
                                        <div>
                                            <h6 class="mb-1"><?php echo htmlspecialchars($doc['nombre_original']); ?></h6>
                                            <small class="text-muted">
                                                Subido el <?php echo date('d/m/Y H:i', strtotime($doc['fecha_subida'])); ?> | 
                                                <?php echo formatFileSize($doc['tamaño_archivo']); ?>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4 text-end">
                                    <div class="btn-group" role="group">
                                        <a href="descargar_documento.php?id=<?php echo $doc['id']; ?>&action=view"
                                           class="btn btn-outline-info btn-sm" target="_blank" title="Ver">
                                            <i class="bi bi-eye me-1"></i>
                                            Ver
                                        </a>
                                        <a href="descargar_documento.php?id=<?php echo $doc['id']; ?>&action=download"
                                           class="btn btn-outline-primary btn-sm" title="Descargar">
                                            <i class="bi bi-download me-1"></i>
                                            Descargar
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <!-- Footer -->
    <footer class="mt-5 py-4 bg-light">
        <div class="container text-center">
            <p class="mb-0 text-muted">
                &copy; <?php echo date('Y'); ?> Gestar servicios. Todos los derechos reservados.
            </p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Log del proyecto para debugging
        console.log('InteletGroup Documentos - Usuario:', '<?php echo htmlspecialchars($nombre_usuario); ?>');
        console.log('Proyecto:', '<?php echo htmlspecialchars($proyecto); ?>');
        console.log('Total Prospectos:', <?php echo count($prospectos_usuario); ?>);

        // Mensaje de inicialización
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Panel de Documentos InteletGroup cargado correctamente');
        });
    </script>
</body>
</html>

<?php
// Función para obtener icono según tipo de archivo
function getFileIcon($mimeType) {
    switch ($mimeType) {
        case 'application/pdf':
            return 'pdf';
        case 'application/msword':
        case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
            return 'word';
        case 'application/vnd.ms-excel':
        case 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
            return 'excel';
        case 'image/jpeg':
        case 'image/jpg':
        case 'image/png':
            return 'image';
        default:
            return 'text';
    }
}

// Función para formatear tamaño de archivo
function formatFileSize($bytes) {
    if ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' bytes';
    }
}
?>
*/
