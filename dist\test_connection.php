<?php
// Test de conexión simple
echo "Probando conexión...\n";

try {
    require_once("con_db.php");
    echo "Conexión exitosa!\n";
    
    if (isset($mysqli)) {
        echo "Variable mysqli disponible\n";
        $result = $mysqli->query("SELECT 1 as test");
        if ($result) {
            echo "Consulta de prueba exitosa\n";
        } else {
            echo "Error en consulta: " . $mysqli->error . "\n";
        }
    } else {
        echo "Variable mysqli no disponible\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>