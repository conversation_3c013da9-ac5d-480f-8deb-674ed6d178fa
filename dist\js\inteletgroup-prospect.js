// JavaScript para el formulario de prospectos InteletGroup

// Variables globales
let currentUserName = '';
let currentUserId = 0;
let documentChecklist = {}; // Para almacenar el checklist de documentos
let uploadedFiles = {}; // Para almacenar los archivos subidos por tipo de documento
let modalInstance = null; // Para mantener una única instancia del modal

// Inicialización cuando el DOM está listo
document.addEventListener('DOMContentLoaded', function() {
    initializeInteletGroupProspectForm();
    
    // Prevenir notificaciones duplicadas de otros scripts
    setupNotificationPrevention();
});

// Función principal de inicialización
function initializeInteletGroupProspectForm() {
    // Obtener información del usuario actual
    if (typeof window.currentUserName !== 'undefined') {
        currentUserName = window.currentUserName;
    }
    if (typeof window.currentUserId !== 'undefined') {
        currentUserId = window.currentUserId;
    }

    // Log de inicialización
    console.log('=== INICIALIZACIÓN INTELETGROUP ===');
    console.log('currentUserName inicializado:', currentUserName);
    console.log('currentUserId inicializado:', currentUserId);
    console.log('window.currentUserName:', window.currentUserName);
    console.log('window.currentUserId:', window.currentUserId);

    // Configurar eventos
    setupFormEvents();
    setupValidation();
    setupFileUpload();

    // Configurar evento del tipo de persona con delegación de eventos
    console.log('Llamando a setupTipoPersonaEvent...');
    setupTipoPersonaEvent();
    console.log('setupTipoPersonaEvent llamada completada');
}

// Configurar eventos del formulario
function setupFormEvents() {
    // Botón de guardar
    const saveBtn = document.getElementById('saveInteletGroupProspectBtn');
    if (saveBtn) {
        // Remover cualquier listener anterior
        saveBtn.replaceWith(saveBtn.cloneNode(true));
        const newSaveBtn = document.getElementById('saveInteletGroupProspectBtn');
        
        // Agregar un único event listener
        newSaveBtn.addEventListener('click', function(event) {
            event.preventDefault();
            event.stopPropagation();

            // Deshabilitar temporalmente el botón para prevenir múltiples clicks
            if (newSaveBtn.disabled || isSubmitting) {
                console.log('Botón ya deshabilitado o envío en progreso');
                return false;
            }

            handleSaveProspect();
            return false;
        });
    }

    // Prevenir envío por Enter en el formulario
    const form = document.getElementById('inteletGroupProspectForm');
    if (form) {
        form.removeEventListener('submit', preventFormSubmit);
        form.addEventListener('submit', preventFormSubmit);
    }

    // Botón de llenar datos de prueba
    const fillTestBtn = document.getElementById('fillTestDataBtn');
    if (fillTestBtn) {
        fillTestBtn.removeEventListener('click', fillTestData);
        fillTestBtn.addEventListener('click', fillTestData);
    }

    // Evento cuando se abre el modal - usar once para evitar duplicados
    const modal = document.getElementById('inteletGroupProspectModal');
    console.log('Modal encontrado:', modal);
    if (modal) {
        console.log('Registrando evento show.bs.modal');
        // Usar eventos nativos de Bootstrap en lugar de jQuery
        modal.removeEventListener('show.bs.modal', handleModalShow);
        modal.addEventListener('show.bs.modal', handleModalShow);
    } else {
        console.log('Modal inteletGroupProspectModal no encontrado');
    }
}

// Función auxiliar para prevenir el envío del formulario
function preventFormSubmit(event) {
    event.preventDefault();
    event.stopPropagation();
    event.stopImmediatePropagation();
    return false;
}

// Función para manejar cuando se muestra el modal
function handleModalShow() {
    console.log('Modal show.bs.modal disparado');
    resetForm();
    populateExecutiveName();

    // Registrar evento para cambio de tipo de persona
    const tipoPersonaField = document.getElementById('tipo_persona');
    if (tipoPersonaField) {
        console.log('Campo tipo_persona encontrado, registrando evento change');
        // Remover listener anterior si existe
        tipoPersonaField.removeEventListener('change', handleTipoPersonaChange);
        // Agregar nuevo listener
        tipoPersonaField.addEventListener('change', handleTipoPersonaChange);
    } else {
        console.log('Campo tipo_persona NO encontrado');
    }
}

// Función para manejar el cambio de tipo de persona
function handleTipoPersonaChange() {
    console.log('Tipo de persona cambiado a:', this.value);
    loadDocumentChecklist(this.value);
}

// Configurar evento del tipo de persona usando delegación de eventos
function setupTipoPersonaEvent() {
    console.log('=== setupTipoPersonaEvent ===');

    // Usar delegación de eventos en el documento para capturar cambios en el select
    document.addEventListener('change', function(event) {
        if (event.target && event.target.id === 'tipo_persona') {
            console.log('Evento change detectado en tipo_persona:', event.target.value);
            loadDocumentChecklist(event.target.value);
        }
    });

    console.log('Evento de delegación registrado para tipo_persona');
}

// Configurar validación en tiempo real
function setupValidation() {
    const form = document.getElementById('inteletGroupProspectForm');
    if (!form) return;

    // RUT validation and formatting
    const rutField = document.getElementById('rut_cliente');
    if (rutField) {
        rutField.addEventListener('blur', function() {
            formatRUT(this);
            validateRUT(this);
        });
    }

    // Razón Social validation and uppercase
    const razonSocialField = document.getElementById('razon_social');
    if (razonSocialField) {
        razonSocialField.addEventListener('blur', function() {
            this.value = this.value.toUpperCase();
            validateRazonSocial(this);
        });
    }

    // Campos de texto que deben ser mayúsculas
    const uppercaseFields = ['razon_social', 'rubro', 'direccion_comercial', 'dias_atencion'];
    uppercaseFields.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (field) {
            // Transformar a mayúsculas mientras se escribe
            field.addEventListener('input', function() {
                const cursorPos = this.selectionStart;
                this.value = this.value.toUpperCase();
                this.setSelectionRange(cursorPos, cursorPos);
            });
            
            // También en blur por si acaso
            field.addEventListener('blur', function() {
                this.value = this.value.toUpperCase();
            });
        }
    });

    // Teléfono validation
    const telefonoField = document.getElementById('telefono_celular');
    if (telefonoField) {
        telefonoField.addEventListener('input', function() {
            validateTelefono(this);
        });
    }

    // Email validation
    const emailField = document.getElementById('email');
    if (emailField) {
        emailField.addEventListener('input', function() {
            validateEmail(this);
        });
    }

    // Validación general para campos requeridos
    const requiredFields = form.querySelectorAll('[required]');
    requiredFields.forEach(field => {
        field.addEventListener('blur', function() {
            validateRequired(this);
        });
    });
}

// Configurar subida de archivos
function setupFileUpload() {
    const fileInput = document.getElementById('documentos');
    if (!fileInput) return;

    fileInput.addEventListener('change', function() {
        validateFiles(this);
    });
}

// Formatear RUT automáticamente
function formatRUT(field) {
    // Guardar posición del cursor
    const cursorPos = field.selectionStart;
    let value = field.value;
    
    // Eliminar todo excepto números y K
    value = value.replace(/[^0-9kK]/g, '');
    
    // Convertir k minúscula a K mayúscula
    value = value.replace(/k/g, 'K');
    
    if (value.length >= 2) {
        // Si tiene más de 8 caracteres antes del guión, tomar solo los primeros 9
        if (value.length > 9) {
            value = value.slice(0, 9);
        }
        
        // Insertar el guión antes del último carácter
        if (value.length >= 8) {
            const numero = value.slice(0, -1);
            const dv = value.slice(-1);
            value = numero + '-' + dv;
        }
    }
    
    field.value = value;
    
    // Restaurar posición del cursor ajustada
    if (cursorPos <= value.length) {
        field.setSelectionRange(cursorPos, cursorPos);
    }
}

// Validar RUT
function validateRUT(field) {
    const value = field.value.trim();
    const rutPattern = /^\d{7,8}-[\dkK]$/;
    
    if (value === '') {
        setFieldState(field, 'neutral');
        return true;
    }
    
    if (!rutPattern.test(value)) {
        setFieldState(field, 'invalid', 'Formato inválido. Use: ********-9');
        return false;
    }
    
    setFieldState(field, 'valid');
    return true;
}

// Validar Razón Social
function validateRazonSocial(field) {
    const value = field.value.trim();
    // Permitir letras mayúsculas, números, espacios y algunos caracteres especiales comunes en nombres de empresas
    const pattern = /^[A-Z0-9\s\.\-\&]+$/;
    
    if (value === '') {
        setFieldState(field, 'neutral');
        return true;
    }
    
    if (!pattern.test(value)) {
        setFieldState(field, 'invalid', 'Solo letras mayúsculas, números, espacios y caracteres: . - &');
        return false;
    }
    
    setFieldState(field, 'valid');
    return true;
}

// Validar teléfono
function validateTelefono(field) {
    const value = field.value.trim();
    const pattern = /^\d{9,15}$/;
    
    if (value === '') {
        setFieldState(field, 'neutral');
        return true;
    }
    
    if (!pattern.test(value)) {
        setFieldState(field, 'invalid', 'Solo números, mínimo 9 dígitos');
        return false;
    }
    
    setFieldState(field, 'valid');
    return true;
}

// Validar email
function validateEmail(field) {
    const value = field.value.trim();
    const pattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    
    if (value === '') {
        setFieldState(field, 'neutral');
        return true;
    }
    
    if (!pattern.test(value)) {
        setFieldState(field, 'invalid', 'Formato de email inválido');
        return false;
    }
    
    setFieldState(field, 'valid');
    return true;
}

// Validar campos requeridos
function validateRequired(field) {
    const value = field.value.trim();
    
    if (value === '') {
        setFieldState(field, 'invalid', 'Este campo es requerido');
        return false;
    }
    
    setFieldState(field, 'valid');
    return true;
}

// Validar archivos
function validateFiles(fileInput) {
    const files = fileInput.files;
    const maxSize = 5 * 1024 * 1024; // 5MB
    const allowedTypes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'image/jpeg',
        'image/jpg',
        'image/png',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ];
    
    let isValid = true;
    let errorMessage = '';
    
    for (let i = 0; i < files.length; i++) {
        const file = files[i];
        
        if (file.size > maxSize) {
            isValid = false;
            errorMessage = `El archivo ${file.name} excede el tamaño máximo de 5MB`;
            break;
        }
        
        if (!allowedTypes.includes(file.type)) {
            isValid = false;
            errorMessage = `El archivo ${file.name} no tiene un formato permitido`;
            break;
        }
    }
    
    if (!isValid) {
        setFieldState(fileInput, 'invalid', errorMessage);
        fileInput.value = '';
    } else {
        setFieldState(fileInput, 'valid');
    }
    
    return isValid;
}

// Establecer estado del campo
function setFieldState(field, state, message = '') {
    const feedback = field.parentNode.querySelector('.invalid-feedback');
    
    // Limpiar clases anteriores
    field.classList.remove('is-valid', 'is-invalid');
    
    switch (state) {
        case 'valid':
            field.classList.add('is-valid');
            if (feedback) feedback.textContent = '';
            break;
        case 'invalid':
            field.classList.add('is-invalid');
            if (feedback) feedback.textContent = message;
            break;
        case 'neutral':
            if (feedback) feedback.textContent = '';
            break;
    }
}

// Poblar nombre del ejecutivo
function populateExecutiveName() {
    console.log('=== DEBUG populateExecutiveName ===');
    console.log('currentUserName:', currentUserName);
    console.log('window.currentUserName:', window.currentUserName);

    const nameField = document.getElementById('nombre_ejecutivo');
    console.log('nameField encontrado:', nameField);

    if (nameField) {
        // Intentar múltiples fuentes para el nombre del usuario
        let userName = currentUserName ||
                      window.currentUserName ||
                      (window.currentUserId ? 'JOHANNA LISSETE RIGO ESPINOZA' : '') ||
                      'Usuario Ejecutivo';

        console.log('userName a usar:', userName);

        // Forzar el llenado del campo
        nameField.value = userName;
        nameField.setAttribute('value', userName);

        // Disparar evento para asegurar que se registre el cambio
        nameField.dispatchEvent(new Event('input', { bubbles: true }));
        nameField.dispatchEvent(new Event('change', { bubbles: true }));

        console.log('Campo nombre_ejecutivo llenado con:', userName);
        console.log('Valor actual del campo:', nameField.value);
    } else {
        console.log('Campo nombre_ejecutivo no encontrado');
    }
}

// Función para generar un RUT único para pruebas
function generateUniqueTestRut() {
    // Generar un número aleatorio entre ******** y 99999999
    const randomNumber = Math.floor(Math.random() * (99999999 - ******** + 1)) + ********;

    // Calcular dígito verificador
    let sum = 0;
    let multiplier = 2;
    const rutString = randomNumber.toString();

    for (let i = rutString.length - 1; i >= 0; i--) {
        sum += parseInt(rutString[i]) * multiplier;
        multiplier = multiplier === 7 ? 2 : multiplier + 1;
    }

    const remainder = sum % 11;
    const dv = remainder < 2 ? remainder.toString() : (11 - remainder === 10 ? 'K' : (11 - remainder).toString());

    return `${randomNumber}-${dv}`;
}

// Llenar datos de prueba
function fillTestData() {
    const uniqueRut = generateUniqueTestRut();
    const testData = {
        tipo_persona: 'Natural',
        rut_cliente: uniqueRut,
        razon_social: 'EMPRESA EJEMPLO LTDA',
        rubro: 'COMERCIO AL POR MENOR',
        direccion_comercial: 'AV PROVIDENCIA 123, PROVIDENCIA, SANTIAGO',
        telefono_celular: '*********',
        email: '<EMAIL>',
        numero_pos: 'POS123456',
        tipo_cuenta: 'Cuenta Corriente',
        numero_cuenta_bancaria: '********',
        dias_atencion: 'LUNES A VIERNES',
        horario_atencion: '09:00 - 18:00',
        contrata_boleta: 'Si',
        competencia_actual: 'Transbank'
    };
    
    Object.keys(testData).forEach(key => {
        const field = document.getElementById(key);
        if (field) {
            field.value = testData[key];
            // Trigger validation and formatting
            field.dispatchEvent(new Event('input'));
            field.dispatchEvent(new Event('blur'));
        }
    });
    
    showMessage('info', 'Datos de prueba cargados correctamente');
}

// Validar todo el formulario
function validateForm() {
    const form = document.getElementById('inteletGroupProspectForm');
    if (!form) return false;
    
    let isValid = true;
    const requiredFields = form.querySelectorAll('[required]');
    
    requiredFields.forEach(field => {
        if (!validateRequired(field)) {
            isValid = false;
        }
    });
    
    // Validaciones específicas
    const rutField = document.getElementById('rut_cliente');
    if (rutField && !validateRUT(rutField)) {
        isValid = false;
    }
    
    const razonSocialField = document.getElementById('razon_social');
    if (razonSocialField && !validateRazonSocial(razonSocialField)) {
        isValid = false;
    }
    
    const telefonoField = document.getElementById('telefono_celular');
    if (telefonoField && !validateTelefono(telefonoField)) {
        isValid = false;
    }
    
    const emailField = document.getElementById('email');
    if (emailField && !validateEmail(emailField)) {
        isValid = false;
    }
    
    return isValid;
}

// Variables globales para controlar envíos múltiples y prevenir duplicados
let isSubmitting = false;
let lastFormData = null;
let lastSubmissionTime = 0;
const MINIMUM_SUBMISSION_INTERVAL = 2000; // 2 segundos mínimo entre envíos
let submissionAbortController = null;
let submissionCount = 0; // Contador de envíos para debugging
let formSubmissionInProgress = false; // Flag adicional para prevenir múltiples envíos

// Manejar guardado del prospecto
async function handleSaveProspect() {
    // 1. Verificar si ya hay un envío en progreso (CRÍTICO para prevenir doble clic)
    if (isSubmitting || formSubmissionInProgress) {
        submissionCount++;
        console.log(`Envío #${submissionCount} bloqueado - ya hay un envío en progreso`);
        return;
    }

    // 2. Verificar intervalo mínimo entre envíos
    const currentTime = Date.now();
    if (currentTime - lastSubmissionTime < MINIMUM_SUBMISSION_INTERVAL) {
        const remainingTime = Math.ceil((MINIMUM_SUBMISSION_INTERVAL - (currentTime - lastSubmissionTime)) / 1000);
        submissionCount++;
        console.log(`Envío #${submissionCount} bloqueado - debe esperar ${remainingTime} segundos`);
        showMessage('error', `Se detectó un posible doble clic. Los mismos datos exactos fueron enviados hace pocos segundos. Si desea crear un nuevo prospecto con los mismos datos, espere unos momentos.`);
        return;
    }

    // 3. Validar formulario en el cliente primero
    if (!validateForm()) {
        // Limpiar cualquier mensaje previo
        clearAllMessages();
        // Mostrar solo en el modal, no como notificación flotante
        showMessage('error', 'Por favor, corrija los errores marcados en el formulario.');
        return;
    }

    // 4. Configurar estado de "cargando" y prevención de duplicados
    const saveBtn = document.getElementById('saveInteletGroupProspectBtn');
    const form = document.getElementById('inteletGroupProspectForm');

    // Establecer flags de control inmediatamente
    isSubmitting = true;
    formSubmissionInProgress = true;
    lastSubmissionTime = currentTime;

    // Deshabilitar el botón inmediatamente
    setLoadingState(saveBtn, true);

    // Cancelar cualquier envío anterior pendiente
    if (submissionAbortController) {
        submissionAbortController.abort();
    }
    submissionAbortController = new AbortController();

    // Limpiar mensajes anteriores antes de mostrar el nuevo
    clearAllMessages();
    showMessage('info', 'Guardando prospecto...');

    // 5. Preparar y validar datos del formulario
    try {
        const formData = new FormData(form);
        formData.append('usuario_id', window.currentUserId);
        
        // 6. Verificar si estos EXACTOS datos ya fueron enviados recientemente (PREVENCIÓN ESTRICTA)
        const currentFormDataString = JSON.stringify({
            rut_cliente: formData.get('rut_cliente'),
            razon_social: formData.get('razon_social'),
            email: formData.get('email'),
            telefono_celular: formData.get('telefono_celular'),
            timestamp: Math.floor(Date.now() / 2000) // Ventanas de 2 segundos
        });

        if (lastFormData === currentFormDataString) {
            submissionCount++;
            console.log(`Envío #${submissionCount} bloqueado - datos idénticos detectados en ventana de tiempo`);
            showMessage('error', 'Se detectó un posible doble clic. Los mismos datos exactos fueron enviados hace pocos segundos. Si desea crear un nuevo prospecto con los mismos datos, espere unos momentos.');
            // Restaurar estado del botón y flags
            setLoadingState(saveBtn, false);
            isSubmitting = false;
            formSubmissionInProgress = false;
            return;
        }

        lastFormData = currentFormDataString;
        submissionCount++;
        console.log(`Iniciando envío #${submissionCount}`);

        // Agregar archivos del checklist
        Object.keys(uploadedFiles).forEach(docType => {
            const files = uploadedFiles[docType];
            files.forEach((file, index) => {
                formData.append(`documento_${docType}[]`, file);
            });
        });
        
        // Agregar información del checklist
        formData.append('checklist_types', JSON.stringify(Object.keys(uploadedFiles)));

        // Debug: Verificar archivos adicionales
        const additionalFiles = document.getElementById('documentos_adicionales');
        if (additionalFiles && additionalFiles.files.length > 0) {
            console.log('Archivos adicionales detectados:', additionalFiles.files.length);
        }

        // Debug: Mostrar contenido de FormData
        console.log('Contenido de FormData:');
        for (let pair of formData.entries()) {
            if (pair[1] instanceof File) {
                console.log(pair[0], '(File):', pair[1].name, pair[1].size, 'bytes');
            } else {
                console.log(pair[0], ':', pair[1]);
            }
        }

        // 7. Enviar datos con fetch y timeout
        const response = await fetch('guardar_inteletgroup_prospecto.php', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            },
            signal: submissionAbortController.signal
        });

        const result = await response.json();

        if (!response.ok || !result.success) {
            // Si la respuesta no es 2xx o success es false, tratarla como un error
            handleServerError(result, response.status);
        } else {
            // Éxito real (respuesta 2xx Y success true)
            handleSuccess(result);
        }

    } catch (error) {
        console.error('Error en la solicitud fetch:', error);

        if (error.name === 'AbortError') {
            console.log('Solicitud cancelada');
            showMessage('info', 'Solicitud cancelada');
        } else {
            showMessage('error', 'Error de conexión. No se pudo contactar al servidor.');
        }

        // Resetear flags en caso de error
        isSubmitting = false;
        formSubmissionInProgress = false;
    } finally {
        // 8. Restaurar estado del botón y limpiar variables de control
        setLoadingState(saveBtn, false);
        isSubmitting = false;
        formSubmissionInProgress = false;
        submissionAbortController = null;

        // Limpiar lastFormData después de un tiempo para permitir reenvíos legítimos
        setTimeout(() => {
            lastFormData = null;
            console.log('Cache de formulario limpiado - permitiendo nuevos envíos');
        }, 2000); // 2 segundos para limpiar
    }
}

// Maneja una respuesta exitosa del servidor
function handleSuccess(result) {
    // Limpiar el mensaje de "Guardando prospecto..." primero
    clearAllMessages();

    // Mostrar mensaje de éxito dentro del modal
    showMessage('success', 'Prospecto registrado exitosamente. Se ha guardado toda la información.');

    // Cerrar modal después de mostrar el mensaje por un momento
    setTimeout(() => {
        if (modalInstance) {
            modalInstance.hide();
        }

        // Mostrar notificación de éxito en la página principal
        crearNotificacionPrincipal('success', '¡Éxito!', 'El prospecto ha sido registrado correctamente en el sistema.');

        // Resetear formulario para el próximo uso
        resetForm();
    }, 2000); // 2 segundos para leer el mensaje en el modal

    // Limpiar variables de control
    isSubmitting = false;
    lastFormData = null;
}

// Maneja una respuesta de error del servidor
function handleServerError(result, status) {
    let errorMessage = result.message || 'Ocurrió un error desconocido.';

    // Limpiar errores previos en los campos
    clearFieldErrors();

    // Limpiar mensaje de "Guardando prospecto..."
    clearAllMessages();

    if (status === 400 && result.errors) {
        // Errores de validación específicos
        errorMessage = 'Se encontraron errores en el formulario:';
        Object.keys(result.errors).forEach(fieldName => {
            const field = document.getElementById(fieldName);
            const errorText = result.errors[fieldName];
            if (field) {
                setFieldState(field, 'invalid', errorText);
            }
            errorMessage += `\n- ${errorText}`;
        });
        // Enfocar el primer campo con error
        const firstErrorField = document.querySelector('.is-invalid');
        if (firstErrorField) firstErrorField.focus();

    } else if (status === 409 && result.errors && result.errors.rut_cliente) {
        // Error específico de RUT duplicado - DESHABILITADO
        // Se permite registrar el mismo RUT múltiples veces
        errorMessage = result.message;
    }

    showMessage('error', errorMessage);
}


// Limpia los errores de todos los campos del formulario
function clearFieldErrors() {
    const form = document.getElementById('inteletGroupProspectForm');
    if (!form) return;
    
    const fields = form.querySelectorAll('.is-invalid');
    fields.forEach(field => {
        setFieldState(field, 'neutral');
    });
}


// Establece el estado de carga de un botón
function setLoadingState(button, isLoading) {
    if (!button) return;
    const btnText = button.querySelector('.btn-text');
    const btnLoading = button.querySelector('.btn-loading');

    if (isLoading) {
        button.disabled = true;
        if (btnText) btnText.style.display = 'none';
        if (btnLoading) btnLoading.style.display = 'inline-block';
    } else {
        button.disabled = false;
        if (btnText) btnText.style.display = 'inline-block';
        if (btnLoading) btnLoading.style.display = 'none';
    }
}



// Limpiar todos los mensajes
function clearAllMessages() {
    const container = document.getElementById('inteletgroup-message-container');
    const successMsg = document.getElementById('inteletgroup-success-message');
    const errorMsg = document.getElementById('inteletgroup-error-message');
    const loadingMsg = document.getElementById('inteletgroup-loading-message');

    if (container) container.style.display = 'none';
    if (successMsg) successMsg.style.display = 'none';
    if (errorMsg) errorMsg.style.display = 'none';
    if (loadingMsg) loadingMsg.style.display = 'none';

    // Limpiar también notificaciones principales
    const notifContainer = document.getElementById('inteletgroup-notifications-container');
    if (notifContainer) {
        notifContainer.innerHTML = '';
    }
}

// Mostrar mensajes en el formulario modal
function showMessage(type, message, inModalOnly = false) {
    const container = document.getElementById('inteletgroup-message-container');
    const successMsg = document.getElementById('inteletgroup-success-message');
    const errorMsg = document.getElementById('inteletgroup-error-message');
    const loadingMsg = document.getElementById('inteletgroup-loading-message');

    if (!container || !successMsg || !errorMsg || !loadingMsg) {
        console.error('Elementos de mensaje no encontrados');
        return;
    }

    // Ocultar todos los mensajes
    successMsg.style.display = 'none';
    errorMsg.style.display = 'none';
    loadingMsg.style.display = 'none';
    
    // Mostrar el mensaje apropiado
    let targetMsg;
    switch (type) {
        case 'success':
            targetMsg = successMsg;
            break;
        case 'error':
            targetMsg = errorMsg;
            break;
        case 'info':
            targetMsg = loadingMsg;
            break;
    }
    
    if (targetMsg) {
        const messageText = targetMsg.querySelector('.message-text');
        if (messageText) {
            messageText.textContent = message;
        }
        targetMsg.style.display = 'block';
        container.style.display = 'block';
        
        // Hacer scroll al mensaje para asegurarnos que sea visible
        container.scrollIntoView({ behavior: 'smooth', block: 'start' });
        
        // Auto-hide después de 4 segundos para mensajes de error
        if (type === 'error') {
            setTimeout(() => {
                container.style.display = 'none';
            }, 4000);
        }
    }
}

// Crear notificación en la página principal (fuera del modal)
function crearNotificacionPrincipal(type, title, message) {
    // No crear notificaciones flotantes si el modal está visible y hay un mensaje de error
    const modal = document.getElementById('inteletGroupProspectModal');
    const modalInstance = modal ? bootstrap.Modal.getInstance(modal) : null;
    if (modalInstance && modalInstance._isShown && type === 'error') {
        // Si el modal está abierto y es un error, no crear notificación flotante
        return;
    }
    
    // Buscar si ya existe el contenedor de notificaciones o crearlo
    let notifContainer = document.getElementById('inteletgroup-notifications-container');
    
    if (!notifContainer) {
        notifContainer = document.createElement('div');
        notifContainer.id = 'inteletgroup-notifications-container';
        notifContainer.style.position = 'fixed';
        notifContainer.style.top = '80px';
        notifContainer.style.right = '20px';
        notifContainer.style.zIndex = '9999';
        notifContainer.style.maxWidth = '350px';
        document.body.appendChild(notifContainer);
    }
    
    // Crear la notificación
    const notif = document.createElement('div');
    notif.className = `alert alert-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'} shadow-sm`;
    notif.style.marginBottom = '10px';
    notif.style.position = 'relative';
    notif.style.animation = 'fadeInRight 0.5s';
    
    // Agregar estilos de animación si no existen
    if (!document.getElementById('notif-animations')) {
        const style = document.createElement('style');
        style.id = 'notif-animations';
        style.textContent = `
            @keyframes fadeInRight {
                from { opacity: 0; transform: translateX(50px); }
                to { opacity: 1; transform: translateX(0); }
            }
            @keyframes fadeOut {
                from { opacity: 1; }
                to { opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    }
    
    // Contenido de la notificación
    notif.innerHTML = `
        <div class="d-flex align-items-center">
            <div class="me-3">
                <i class="bi ${type === 'success' ? 'bi-check-circle-fill' : 
                          type === 'error' ? 'bi-exclamation-triangle-fill' : 
                          'bi-info-circle-fill'}" style="font-size: 1.5rem;"></i>
            </div>
            <div>
                <h6 class="alert-heading mb-1">${title}</h6>
                <p class="mb-0 small">${message}</p>
            </div>
        </div>
        <button type="button" class="btn-close btn-sm position-absolute" 
                style="top: 10px; right: 10px;" aria-label="Close"></button>
    `;
    
    // Agregar la notificación al contenedor
    notifContainer.appendChild(notif);
    
    // Agregar evento para cerrar la notificación
    const closeBtn = notif.querySelector('.btn-close');
    if (closeBtn) {
        closeBtn.addEventListener('click', function() {
            notif.style.animation = 'fadeOut 0.3s';
            setTimeout(() => {
                notifContainer.removeChild(notif);
                // Si no quedan notificaciones, remover el contenedor
                if (notifContainer.children.length === 0) {
                    document.body.removeChild(notifContainer);
                }
            }, 300);
        });
    }
    
    // Auto-ocultar después de 4 segundos para mensajes de éxito, 8 para otros
    const hideDelay = type === 'success' ? 4000 : 8000;
    setTimeout(() => {
        if (notif.parentNode) {
            notif.style.animation = 'fadeOut 0.3s';
            setTimeout(() => {
                if (notif.parentNode) {
                    notifContainer.removeChild(notif);
                    // Si no quedan notificaciones, remover el contenedor
                    if (notifContainer.children.length === 0 && notifContainer.parentNode) {
                        document.body.removeChild(notifContainer);
                    }
                }
            }, 300);
        }
    }, hideDelay);
    
    return notif;
}

// Resetear formulario
function resetForm() {
    const form = document.getElementById('inteletGroupProspectForm');
    if (form) {
        form.reset();

        // Limpiar estados de validación
        const fields = form.querySelectorAll('.form-control, .form-select');
        fields.forEach(field => {
            field.classList.remove('is-valid', 'is-invalid');
        });

        // Limpiar mensajes
        const feedbacks = form.querySelectorAll('.invalid-feedback');
        feedbacks.forEach(feedback => {
            feedback.textContent = '';
        });

        // Resetear el botón de guardar
        const saveBtn = document.getElementById('saveInteletGroupProspectBtn');
        if (saveBtn) {
            saveBtn.disabled = false;
            saveBtn.classList.remove('loading');

            const btnText = saveBtn.querySelector('.btn-text');
            const btnLoading = saveBtn.querySelector('.btn-loading');
            if (btnText) btnText.style.display = 'inline-block';
            if (btnLoading) btnLoading.style.display = 'none';
        }

        // Repoblar el nombre del ejecutivo
        populateExecutiveName();
    }

    // Limpiar checklist de documentos
    uploadedFiles = {};
    const checklistContainer = document.getElementById('document-checklist-container');
    if (checklistContainer) {
        checklistContainer.innerHTML =
            '<p class="text-muted">Seleccione un tipo de persona para ver los documentos requeridos.</p>';
    }

    // Ocultar mensajes
    const container = document.getElementById('inteletgroup-message-container');
    if (container) {
        container.style.display = 'none';
    }

    // Limpiar variables de control
    isSubmitting = false;
    formSubmissionInProgress = false;
    lastFormData = null;
}

// Función para abrir el modal (llamada desde el botón)
function abrirModalInteletGroupProspecto() {
    const modalElement = document.getElementById('inteletGroupProspectModal');
    
    // Crear o reutilizar la instancia única del modal
    if (!modalInstance) {
        modalInstance = new bootstrap.Modal(modalElement);
    }
    
    modalInstance.show();

    // Asegurar que el nombre del ejecutivo se llene después de abrir el modal
    setTimeout(() => {
        populateExecutiveName();
    }, 100);
}

// Función para generar RUT aleatorio válido
function generarRutAleatorio() {
    // Generar número base aleatorio entre 10.000.000 y 99.999.999
    const numeroBase = Math.floor(Math.random() * ********) + ********;

    // Calcular dígito verificador
    const digitoVerificador = calcularDigitoVerificador(numeroBase);

    // Formatear RUT
    const rutGenerado = numeroBase + '-' + digitoVerificador;

    // Asignar al campo
    const campoRut = document.getElementById('rut_cliente');
    if (campoRut) {
        campoRut.value = rutGenerado;
        campoRut.classList.remove('is-invalid');

        // Mostrar notificación temporal
        const button = event.target.closest('button');
        const originalContent = button.innerHTML;
        button.innerHTML = '<i class="bi bi-check"></i>';
        button.classList.add('btn-success');
        button.classList.remove('btn-outline-secondary');

        setTimeout(() => {
            button.innerHTML = originalContent;
            button.classList.remove('btn-success');
            button.classList.add('btn-outline-secondary');
        }, 1000);
    }

    console.log('RUT aleatorio generado:', rutGenerado);
}

// Función para calcular dígito verificador de RUT
function calcularDigitoVerificador(rut) {
    let suma = 0;
    let multiplicador = 2;

    // Convertir a string y procesar de derecha a izquierda
    const rutStr = rut.toString();
    for (let i = rutStr.length - 1; i >= 0; i--) {
        suma += parseInt(rutStr[i]) * multiplicador;
        multiplicador = multiplicador === 7 ? 2 : multiplicador + 1;
    }

    const resto = suma % 11;
    const dv = 11 - resto;

    if (dv === 11) return '0';
    if (dv === 10) return 'K';
    return dv.toString();
}

// Función para llenar datos de prueba
function llenarDatosPrueba() {
    // Generar RUT aleatorio primero
    generarRutAleatorio();

    // Datos de prueba para InteletGroup (todo en mayúsculas donde corresponde)
    const datosPrueba = {
        nombre_ejecutivo: window.currentUserName || 'EJECUTIVO TEST',
        razon_social: 'EMPRESA EJEMPLO LTDA',
        rubro: 'COMERCIO AL POR MENOR',
        direccion_comercial: 'AV. PROVIDENCIA 1234, SANTIAGO',
        telefono_celular: '9' + Math.floor(Math.random() * ******** + ********),
        email: '<EMAIL>',
        numero_pos: 'POS' + Math.floor(Math.random() * 900000 + 100000),
        tipo_cuenta: 'Cuenta Corriente',
        numero_cuenta_bancaria: Math.floor(Math.random() * ********** + **********).toString(),
        dias_atencion: 'LUNES A VIERNES',
        horario_atencion: '09:00 - 18:00',
        contrata_boleta: 'Si',
        competencia_actual: 'Transbank'
    };

    // Llenar los campos del formulario
    Object.keys(datosPrueba).forEach(campo => {
        const elemento = document.getElementById('inteletgroup_' + campo) || document.getElementById(campo);
        if (elemento) {
            elemento.value = datosPrueba[campo];
            // Trigger eventos para aplicar formato
            elemento.dispatchEvent(new Event('input'));
            elemento.dispatchEvent(new Event('blur'));
        }
    });

    console.log('Datos de prueba cargados para InteletGroup');
}

// Función para prevenir notificaciones duplicadas
function setupNotificationPrevention() {
    // Interceptar console.error para evitar que otros scripts creen notificaciones
    const originalConsoleError = console.error;
    console.error = function() {
        // Si el modal de InteletGroup está abierto, no permitir notificaciones externas
        const modal = document.getElementById('inteletGroupProspectModal');
        const modalInstance = modal ? bootstrap.Modal.getInstance(modal) : null;
        if (modalInstance && modalInstance._isShown) {
            // Solo log en consola, no crear notificaciones
            originalConsoleError.apply(console, arguments);
            return;
        }
        // Si el modal no está abierto, comportamiento normal
        originalConsoleError.apply(console, arguments);
    };
}

// =============================
// FUNCIONES PARA CHECKLIST DE DOCUMENTOS
// =============================

// Definición de documentos requeridos por tipo de persona
const DOCUMENT_TYPES = {
    Natural: [
        {
            id: 'PN_CEDULA_FRONTAL',
            name: 'Cédula de identidad frontal',
            description: 'Fotografía o escaneo del frente de la cédula de identidad',
            required: true,
            accept: 'image/*,.pdf'
        },
        {
            id: 'PN_CEDULA_TRASERA',
            name: 'Cédula de identidad trasera',
            description: 'Fotografía o escaneo del reverso de la cédula de identidad',
            required: true,
            accept: 'image/*,.pdf'
        },
        {
            id: 'PN_BOLETA_PATENTE',
            name: 'Boleta y/o Patente Comercial',
            description: 'Documento que acredite la actividad comercial',
            required: true,
            accept: '.pdf,.doc,.docx,image/*'
        },
        {
            id: 'PN_FOTO_INTERIOR',
            name: 'Fotografía interior del establecimiento',
            description: 'Fotografía del interior del establecimiento que muestre la actividad económica',
            required: true,
            accept: 'image/*'
        },
        {
            id: 'PN_FOTO_EXTERIOR',
            name: 'Fotografía exterior del establecimiento',
            description: 'Fotografía del exterior del establecimiento comercial, que muestre fachada y dirección',
            required: true,
            accept: 'image/*'
        },
        {
            id: 'PN_RESPALDO_CUENTA',
            name: 'Respaldo de cuenta de Abono',
            description: 'Documento que indique Titular, número de cuenta y logo del banco (Cta Cte o Cta Vista)',
            required: true,
            accept: '.pdf,.doc,.docx,image/*'
        },
        {
            id: 'AMBOS_CARPETA_TRIBUTARIA',
            name: 'Carpeta Tributaria (FAPRO)',
            description: 'Carpeta tributaria del SII',
            required: false,
            accept: '.pdf,.doc,.docx'
        },
        {
            id: 'AMBOS_MANDATO_PAC',
            name: 'Mandato PAC',
            description: 'Para clientes con cuenta de abono distinta a BCI donde el titular es el establecimiento',
            required: false,
            accept: '.pdf,.doc,.docx'
        },
        {
            id: 'AMBOS_PODER_NOTARIAL',
            name: 'Poder Notarial',
            description: 'Para clientes con cuenta de abono distinta a BCI, donde el titular es distinto al establecimiento',
            required: false,
            accept: '.pdf,.doc,.docx'
        },
        {
            id: 'AMBOS_PODER_NOTARIAL_SIMPLE',
            name: 'Poder Notarial Simple',
            description: 'Poder simple para representación legal',
            required: false,
            accept: '.pdf,.doc,.docx'
        },
        {
            id: 'AMBOS_CARTA_ACEPTACION',
            name: 'Carta de Aceptación',
            description: 'Excepción: Clientes con facturación inferior a 100UF, Carta de Aceptación a la comisión garantizada',
            required: false,
            accept: '.pdf,.doc,.docx'
        },
        // Documentos complementarios (aplican para ambos tipos de persona)
        {
            id: 'AMBOS_CUENTAS_TERCEROS',
            name: 'Cuentas de abono de terceros',
            description: 'Rut titular distinto al del establecimiento - Poder Simple: Cuenta de tercero de único Socio (EIRL)',
            required: false,
            accept: '.pdf,.doc,.docx'
        },
        {
            id: 'AMBOS_SOCIEDAD_HECHO',
            name: 'Sociedad de Hecho - Declaración Jurada Notarial',
            description: 'Declaración Jurada Notarial que indique los representantes legales vigentes a la fecha de firma con contrato BCIPagos. (No tienen escritura pública, ni estatutos)',
            required: false,
            accept: '.pdf,.doc,.docx'
        }
    ],
    Juridica: [
        {
            id: 'PJ_CEDULA_REPRESENTANTES',
            name: 'Cédula de identidad representantes legales',
            description: 'Cédula de identidad por ambos lados, de él o los representantes legales',
            required: true,
            accept: 'image/*,.pdf'
        },
        {
            id: 'PJ_CONSTITUCION_PODERES',
            name: 'Constitución o Poderes Vigentes',
            description: 'Estatutos y Vigencia (FAPRO)',
            required: true,
            accept: '.pdf,.doc,.docx'
        },
        {
            id: 'PJ_BOLETA_PATENTE',
            name: 'Boleta y/o Patente Comercial',
            description: 'Documento que acredite la actividad comercial',
            required: true,
            accept: '.pdf,.doc,.docx,image/*'
        },
        {
            id: 'PJ_FOTO_INTERIOR',
            name: 'Fotografía interior del establecimiento',
            description: 'Fotografía del interior del establecimiento que muestre la actividad económica',
            required: true,
            accept: 'image/*'
        },
        {
            id: 'PJ_FOTO_EXTERIOR',
            name: 'Fotografía exterior del establecimiento',
            description: 'Fotografía del exterior del establecimiento comercial, que muestre fachada y dirección',
            required: true,
            accept: 'image/*'
        },
        {
            id: 'PJ_RESPALDO_CUENTA',
            name: 'Respaldo de cuenta de Abono',
            description: 'Documento que indique Titular, número de cuenta y logo del banco (Cta Cte o Cta Vista)',
            required: true,
            accept: '.pdf,.doc,.docx,image/*'
        },
        {
            id: 'PJ_CARPETA_TRIBUTARIA',
            name: 'Carpeta Tributaria (FAPRO)',
            description: 'Carpeta tributaria del SII',
            required: true,
            accept: '.pdf,.doc,.docx'
        },
        {
            id: 'PJ_ERUT',
            name: 'E-Rut (FAPRO)',
            description: 'E-Rut que indique nombre del representante que firma',
            required: false,
            accept: '.pdf,.doc,.docx'
        },
        // Documentos complementarios (aplican para ambos tipos de persona)
        {
            id: 'AMBOS_CARPETA_TRIBUTARIA',
            name: 'Carpeta Tributaria (FAPRO)',
            description: 'Carpeta tributaria del SII',
            required: false,
            accept: '.pdf,.doc,.docx'
        },
        {
            id: 'AMBOS_MANDATO_PAC',
            name: 'Mandato PAC',
            description: 'Para clientes con cuenta de abono distinta a BCI donde el titular es el establecimiento',
            required: false,
            accept: '.pdf,.doc,.docx'
        },
        {
            id: 'AMBOS_PODER_NOTARIAL',
            name: 'Poder Notarial',
            description: 'Para clientes con cuenta de abono distinta a BCI, donde el titular es distinto al establecimiento',
            required: false,
            accept: '.pdf,.doc,.docx'
        },
        {
            id: 'AMBOS_PODER_NOTARIAL_SIMPLE',
            name: 'Poder Notarial Simple',
            description: 'Poder simple para representación legal',
            required: false,
            accept: '.pdf,.doc,.docx'
        },
        {
            id: 'AMBOS_CARTA_ACEPTACION',
            name: 'Carta de Aceptación',
            description: 'Excepción: Clientes con facturación inferior a 100UF, Carta de Aceptación a la comisión garantizada',
            required: false,
            accept: '.pdf,.doc,.docx'
        },
        {
            id: 'AMBOS_CUENTAS_TERCEROS',
            name: 'Cuentas de abono de terceros',
            description: 'Rut titular distinto al del establecimiento - Poder Simple: Cuenta de tercero de único Socio (EIRL)',
            required: false,
            accept: '.pdf,.doc,.docx'
        },
        {
            id: 'AMBOS_SOCIEDAD_HECHO',
            name: 'Sociedad de Hecho - Declaración Jurada Notarial',
            description: 'Declaración Jurada Notarial que indique los representantes legales vigentes a la fecha de firma con contrato BCIPagos. (No tienen escritura pública, ni estatutos)',
            required: false,
            accept: '.pdf,.doc,.docx'
        }
    ]
};

// Cargar el checklist de documentos según el tipo de persona
function loadDocumentChecklist(tipoPersona) {
    console.log('=== loadDocumentChecklist llamada con:', tipoPersona);
    const container = document.getElementById('document-checklist-container');
    
    if (!container) {
        console.error('Container document-checklist-container no encontrado');
        return;
    }
    
    if (!tipoPersona) {
        console.log('No se proporcionó tipo de persona');
        container.innerHTML = '<p class="text-muted">Seleccione un tipo de persona para ver los documentos requeridos.</p>';
        return;
    }

    // Limpiar contenedor y archivos previos
    container.innerHTML = '';
    uploadedFiles = {};

    // Mostrar loading
    container.innerHTML = '<div class="text-center p-3"><i class="bi bi-hourglass-split me-2"></i>Cargando documentos desde base de datos...</div>';

    // Cargar documentos desde la base de datos
    fetch('endpoints/obtener_tipos_documento_v2.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ tipo_persona: tipoPersona })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.documentos) {
            console.log('Documentos cargados desde BD:', data.documentos);
            renderDocumentChecklistFromDB(data.documentos);
        } else {
            console.error('Error cargando documentos desde BD:', data.error);
            // Fallback al array hardcodeado si falla la consulta
            const documents = DOCUMENT_TYPES[tipoPersona] || [];
            renderDocumentChecklistFromArray(documents);
        }
    })
    .catch(error => {
        console.error('Error de conexión:', error);
        // Fallback al array hardcodeado si falla la conexión
        const documents = DOCUMENT_TYPES[tipoPersona] || [];
        renderDocumentChecklistFromArray(documents);
    });
}

// Función para renderizar el checklist desde datos de la BD
function renderDocumentChecklistFromDB(documentos) {
    const container = document.getElementById('document-checklist-container');
    if (!container) return;

    if (documentos.length === 0) {
        container.innerHTML = '<p class="text-muted">No hay documentos configurados para este tipo de persona.</p>';
        return;
    }

    // Convertir documentos de BD a formato esperado por el frontend
    const documents = documentos.map(doc => ({
        id: doc.codigo,
        name: doc.nombre,
        description: doc.descripcion,
        required: doc.es_obligatorio === 1,
        accept: '.pdf,.doc,.docx,.jpg,.jpeg,.png'
    }));

    renderDocumentChecklist(documents);
}

// Función fallback para renderizar desde array hardcodeado
function renderDocumentChecklistFromArray(documents) {
    if (documents.length === 0) {
        const container = document.getElementById('document-checklist-container');
        container.innerHTML = '<p class="text-muted">Seleccione un tipo de persona para ver los documentos requeridos.</p>';
        return;
    }

    renderDocumentChecklist(documents);
}

// Función común para renderizar el checklist
function renderDocumentChecklist(documents) {
    const container = document.getElementById('document-checklist-container');
    if (!container) return;

    // Crear el HTML del checklist
    let html = '<div class="document-checklist">';

    // Barra de progreso
    html += `
        <div class="checklist-progress mb-3">
            <div class="progress-info">
                <span>Progreso de documentación</span>
                <span id="checklist-progress-text">0 de ${documents.filter(d => d.required).length} obligatorios</span>
            </div>
            <div class="progress">
                <div class="progress-bar bg-success" id="checklist-progress-bar"
                     role="progressbar" style="width: 0%"></div>
            </div>
        </div>
    `;

    // Documentos obligatorios
    const requiredDocs = documents.filter(d => d.required);
    const optionalDocs = documents.filter(d => !d.required);
    
    if (requiredDocs.length > 0) {
        html += '<h6 class="document-category-title"><i class="bi bi-exclamation-circle me-2"></i>Documentos Obligatorios</h6>';
        requiredDocs.forEach(doc => {
            html += createDocumentChecklistItem(doc);
        });
    }
    
    if (optionalDocs.length > 0) {
        html += '<h6 class="document-category-title mt-4"><i class="bi bi-info-circle me-2"></i>Documentos Opcionales</h6>';
        optionalDocs.forEach(doc => {
            html += createDocumentChecklistItem(doc);
        });
    }
    
    html += '</div>';
    container.innerHTML = html;

    // Configurar eventos de upload
    setupChecklistEvents();
}

// Crear HTML para un item del checklist
function createDocumentChecklistItem(doc) {
    const requiredBadge = doc.required ? 
        '<span class="required-badge">Obligatorio</span>' : 
        '<span class="optional-badge">Opcional</span>';
    
    return `
        <div class="checklist-item" data-doc-id="${doc.id}">
            <div class="checklist-item-header">
                <h6 class="checklist-item-title">
                    <i class="bi bi-file-earmark me-2"></i>
                    ${doc.name}
                    ${requiredBadge}
                </h6>
                <div class="checklist-status status-pending">
                    <i class="bi bi-clock-fill" style="font-size: 1.2rem;"></i>
                    <span>Pendiente</span>
                </div>
            </div>
            <div class="checklist-item-description">${doc.description}</div>
            <div class="checklist-upload-area" onclick="triggerFileUpload('${doc.id}')">
                <i class="bi bi-cloud-upload fs-3 text-muted"></i>
                <p class="mb-0 mt-2">Haga clic para seleccionar archivos o arrástrelos aquí</p>
                <small class="text-muted">Formatos permitidos: ${getAcceptFormats(doc.accept)}</small>
            </div>
            <input type="file" 
                   class="checklist-file-input" 
                   id="file-${doc.id}" 
                   data-doc-id="${doc.id}"
                   accept="${doc.accept}"
                   multiple>
            <div class="uploaded-files-list" id="files-list-${doc.id}"></div>
        </div>
    `;
}

// Obtener formatos aceptados en formato legible
function getAcceptFormats(accept) {
    const formats = {
        'image/*': 'Imágenes (JPG, PNG, etc.)',
        '.pdf': 'PDF',
        '.doc,.docx': 'Word',
        '.xlsx,.xls': 'Excel'
    };
    
    return accept.split(',').map(f => formats[f] || f).join(', ');
}

// Configurar eventos del checklist
function setupChecklistEvents() {
    // Eventos para cada input de archivo
    document.querySelectorAll('.checklist-file-input').forEach(input => {
        input.addEventListener('change', handleFileSelection);
    });
    
    // Drag and drop
    document.querySelectorAll('.checklist-upload-area').forEach(area => {
        area.addEventListener('dragover', handleDragOver);
        area.addEventListener('dragleave', handleDragLeave);
        area.addEventListener('drop', handleDrop);
    });
}

// Trigger para abrir el diálogo de archivos
function triggerFileUpload(docId) {
    document.getElementById(`file-${docId}`).click();
}

// Manejar selección de archivos
function handleFileSelection(event) {
    const input = event.target;
    const docId = input.dataset.docId;
    const files = Array.from(input.files);
    
    if (files.length === 0) return;
    
    // Inicializar array si no existe
    if (!uploadedFiles[docId]) {
        uploadedFiles[docId] = [];
    }
    
    // Validar y agregar archivos
    files.forEach(file => {
        if (validateFile(file)) {
            uploadedFiles[docId].push(file);
        }
    });
    
    // Actualizar UI
    updateChecklistItem(docId);
    updateProgress();
    
    // Limpiar input para permitir reselección
    input.value = '';
}

// Validar archivo
function validateFile(file) {
    const maxSize = 5 * 1024 * 1024; // 5MB
    
    if (file.size > maxSize) {
        showMessage('error', `El archivo ${file.name} excede el tamaño máximo de 5MB`);
        return false;
    }
    
    return true;
}

// Actualizar item del checklist
function updateChecklistItem(docId) {
    const item = document.querySelector(`[data-doc-id="${docId}"]`);
    const filesList = document.getElementById(`files-list-${docId}`);
    const status = item.querySelector('.checklist-status');
    const files = uploadedFiles[docId] || [];
    
    if (files.length > 0) {
        // Actualizar estado
        status.className = 'checklist-status status-uploaded';
        status.innerHTML = '<i class="bi bi-check-circle-fill" style="font-size: 1.2rem;"></i><span>Subido</span>';
        item.classList.add('is-valid');
        
        // Mostrar lista de archivos
        let filesHtml = '';
        files.forEach((file, index) => {
            filesHtml += `
                <div class="uploaded-file-item">
                    <span class="uploaded-file-name">
                        <i class="bi bi-file-earmark-fill"></i>
                        ${file.name}
                        <small class="text-muted">(${formatFileSize(file.size)})</small>
                    </span>
                    <button type="button" class="btn-remove-file" 
                            onclick="removeFile('${docId}', ${index})">
                        <i class="bi bi-x"></i>
                    </button>
                </div>
            `;
        });
        filesList.innerHTML = filesHtml;
    } else {
        // Sin archivos
        status.className = 'checklist-status status-pending';
        status.innerHTML = '<i class="bi bi-clock-fill" style="font-size: 1.2rem;"></i><span>Pendiente</span>';
        item.classList.remove('is-valid');
        filesList.innerHTML = '';
    }
}

// Formatear tamaño de archivo
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Eliminar archivo
function removeFile(docId, index) {
    if (uploadedFiles[docId]) {
        uploadedFiles[docId].splice(index, 1);
        updateChecklistItem(docId);
        updateProgress();
    }
}

// Actualizar barra de progreso
function updateProgress() {
    const tipoPersona = document.getElementById('tipo_persona').value;
    if (!tipoPersona) return;
    
    const documents = DOCUMENT_TYPES[tipoPersona] || [];
    const requiredDocs = documents.filter(d => d.required);
    const uploadedRequired = requiredDocs.filter(d => 
        uploadedFiles[d.id] && uploadedFiles[d.id].length > 0
    ).length;
    
    const percentage = requiredDocs.length > 0 ? 
        (uploadedRequired / requiredDocs.length) * 100 : 0;
    
    document.getElementById('checklist-progress-bar').style.width = percentage + '%';
    document.getElementById('checklist-progress-text').textContent = 
        `${uploadedRequired} de ${requiredDocs.length} obligatorios`;
}

// Drag and drop handlers
function handleDragOver(e) {
    e.preventDefault();
    e.currentTarget.classList.add('drag-over');
}

function handleDragLeave(e) {
    e.currentTarget.classList.remove('drag-over');
}

function handleDrop(e) {
    e.preventDefault();
    e.currentTarget.classList.remove('drag-over');
    
    const docId = e.currentTarget.parentElement.dataset.docId;
    const input = document.getElementById(`file-${docId}`);
    
    // Asignar archivos al input y disparar evento
    input.files = e.dataTransfer.files;
    input.dispatchEvent(new Event('change', { bubbles: true }));
}
