<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test - Corrección de Duplicados InteletGroup</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .test-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
        }
        .test-log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            margin-top: 1rem;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 0.875rem;
        }
        .log-entry {
            margin-bottom: 0.5rem;
            padding: 0.25rem;
            border-radius: 0.25rem;
        }
        .log-info { background-color: #d1ecf1; }
        .log-success { background-color: #d4edda; }
        .log-error { background-color: #f8d7da; }
        .log-warning { background-color: #fff3cd; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="mb-4">Test - Corrección de Duplicados InteletGroup</h1>
        
        <div class="alert alert-info">
            <h5>Objetivo del Test</h5>
            <p>Verificar que las correcciones aplicadas al sistema de prevención de duplicados funcionen correctamente:</p>
            <ul>
                <li>Intervalo mínimo reducido de 2s a 1s</li>
                <li>Verificación de duplicados mejorada</li>
                <li>Mensajes de error más claros</li>
                <li>Limpieza más rápida de variables de control</li>
            </ul>
        </div>

        <div class="row">
            <div class="col-md-6">
                <h3>Controles de Prueba</h3>
                <button id="openModalBtn" class="btn btn-primary mb-2 w-100">
                    Abrir Modal InteletGroup
                </button>
                <button id="fillFormBtn" class="btn btn-secondary mb-2 w-100">
                    Llenar Formulario de Prueba
                </button>
                <button id="submitFormBtn" class="btn btn-success mb-2 w-100">
                    Enviar Formulario
                </button>
                <button id="rapidSubmitBtn" class="btn btn-warning mb-2 w-100">
                    Test Envío Rápido (3 clicks)
                </button>
                <button id="clearLogBtn" class="btn btn-outline-secondary mb-2 w-100">
                    Limpiar Log
                </button>
            </div>
            <div class="col-md-6">
                <h3>Estado del Sistema</h3>
                <div class="card">
                    <div class="card-body">
                        <p><strong>isSubmitting:</strong> <span id="isSubmittingStatus">false</span></p>
                        <p><strong>formSubmissionInProgress:</strong> <span id="formProgressStatus">false</span></p>
                        <p><strong>lastSubmissionTime:</strong> <span id="lastTimeStatus">0</span></p>
                        <p><strong>submissionCount:</strong> <span id="submissionCountStatus">0</span></p>
                        <p><strong>Modal Instance:</strong> <span id="modalInstanceStatus">null</span></p>
                    </div>
                </div>
            </div>
        </div>

        <h3 class="mt-4">Log de Eventos</h3>
        <div id="testLog" class="test-log"></div>
    </div>

    <!-- Modal del formulario de prospectos -->
    <div class="modal fade" id="inteletGroupProspectModal" tabindex="-1" aria-labelledby="inteletGroupProspectModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="inteletGroupProspectModalLabel">Test - Registro de Prospecto InteletGroup</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="inteletGroupProspectForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="nombre_ejecutivo" class="form-label">Nombre Ejecutivo</label>
                                    <input type="text" class="form-control" id="nombre_ejecutivo" name="nombre_ejecutivo" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="rut_cliente" class="form-label">RUT Cliente</label>
                                    <input type="text" class="form-control" id="rut_cliente" name="rut_cliente" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="razon_social" class="form-label">Razón Social</label>
                                    <input type="text" class="form-control" id="razon_social" name="razon_social" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email" class="form-label">Email</label>
                                    <input type="email" class="form-control" id="email" name="email" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="telefono_celular" class="form-label">Teléfono</label>
                                    <input type="text" class="form-control" id="telefono_celular" name="telefono_celular" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="tipo_persona" class="form-label">Tipo de Persona</label>
                                    <select class="form-control" id="tipo_persona" name="tipo_persona" required>
                                        <option value="">Seleccione...</option>
                                        <option value="Natural">Persona Natural</option>
                                        <option value="Juridica">Persona Jurídica</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
                    <button type="button" class="btn btn-primary" id="saveInteletGroupProspectBtn">Guardar Prospecto</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Simulación de variables globales -->
    <script>
        window.currentUserId = 999; // Usuario de prueba
        window.currentUserName = 'Usuario Test';
    </script>
    
    <!-- JavaScript del formulario de prospectos -->
    <script src="js/inteletgroup-prospect.js?v=<?php echo time(); ?>"></script>
    
    <!-- Script de prueba -->
    <script>
        // Variables para el test
        let testLogElement;
        let statusUpdateInterval;
        
        document.addEventListener('DOMContentLoaded', function() {
            testLogElement = document.getElementById('testLog');
            
            // Configurar botones de prueba
            setupTestButtons();
            
            // Actualizar estado cada segundo
            statusUpdateInterval = setInterval(updateStatus, 1000);
            
            logEvent('info', 'Test iniciado - Sistema de prevención de duplicados cargado');
        });
        
        function setupTestButtons() {
            document.getElementById('openModalBtn').addEventListener('click', function() {
                logEvent('info', 'Abriendo modal...');
                if (typeof abrirModalInteletGroupProspecto === 'function') {
                    abrirModalInteletGroupProspecto();
                } else {
                    logEvent('error', 'Función abrirModalInteletGroupProspecto no encontrada');
                }
            });
            
            document.getElementById('fillFormBtn').addEventListener('click', function() {
                logEvent('info', 'Llenando formulario con datos de prueba...');
                fillTestForm();
            });
            
            document.getElementById('submitFormBtn').addEventListener('click', function() {
                logEvent('info', 'Enviando formulario...');
                if (typeof handleSaveProspect === 'function') {
                    handleSaveProspect();
                } else {
                    logEvent('error', 'Función handleSaveProspect no encontrada');
                }
            });
            
            document.getElementById('rapidSubmitBtn').addEventListener('click', function() {
                logEvent('warning', 'Iniciando test de envío rápido (3 clicks)...');
                rapidSubmitTest();
            });
            
            document.getElementById('clearLogBtn').addEventListener('click', function() {
                testLogElement.innerHTML = '';
                logEvent('info', 'Log limpiado');
            });
        }
        
        function fillTestForm() {
            const timestamp = Date.now();
            document.getElementById('nombre_ejecutivo').value = 'Test Usuario';
            document.getElementById('rut_cliente').value = '12345678-9';
            document.getElementById('razon_social').value = 'EMPRESA TEST ' + timestamp;
            document.getElementById('email').value = 'test' + timestamp + '@test.com';
            document.getElementById('telefono_celular').value = '987654321';
            document.getElementById('tipo_persona').value = 'Natural';
            logEvent('success', 'Formulario llenado con datos únicos');
        }
        
        function rapidSubmitTest() {
            let clickCount = 0;
            const interval = setInterval(() => {
                clickCount++;
                logEvent('warning', `Click rápido #${clickCount}`);
                
                if (typeof handleSaveProspect === 'function') {
                    handleSaveProspect();
                } else {
                    logEvent('error', 'Función handleSaveProspect no encontrada');
                }
                
                if (clickCount >= 3) {
                    clearInterval(interval);
                    logEvent('info', 'Test de envío rápido completado');
                }
            }, 200); // 200ms entre clicks
        }
        
        function updateStatus() {
            // Actualizar estado de variables globales
            document.getElementById('isSubmittingStatus').textContent = 
                typeof isSubmitting !== 'undefined' ? isSubmitting : 'undefined';
            document.getElementById('formProgressStatus').textContent = 
                typeof formSubmissionInProgress !== 'undefined' ? formSubmissionInProgress : 'undefined';
            document.getElementById('lastTimeStatus').textContent = 
                typeof lastSubmissionTime !== 'undefined' ? lastSubmissionTime : 'undefined';
            document.getElementById('submissionCountStatus').textContent = 
                typeof submissionCount !== 'undefined' ? submissionCount : 'undefined';
            document.getElementById('modalInstanceStatus').textContent = 
                typeof modalInstance !== 'undefined' ? (modalInstance ? 'exists' : 'null') : 'undefined';
        }
        
        function logEvent(type, message) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            testLogElement.appendChild(logEntry);
            testLogElement.scrollTop = testLogElement.scrollHeight;
        }
        
        // Interceptar console.log para capturar eventos del sistema
        const originalConsoleLog = console.log;
        console.log = function() {
            originalConsoleLog.apply(console, arguments);
            if (arguments.length > 0 && typeof arguments[0] === 'string') {
                logEvent('info', 'Console: ' + arguments[0]);
            }
        };
        
        // Interceptar console.error para capturar errores
        const originalConsoleError = console.error;
        console.error = function() {
            originalConsoleError.apply(console, arguments);
            if (arguments.length > 0 && typeof arguments[0] === 'string') {
                logEvent('error', 'Error: ' + arguments[0]);
            }
        };
    </script>
</body>
</html>
